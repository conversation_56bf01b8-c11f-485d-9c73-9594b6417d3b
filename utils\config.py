"""
Configuration management for the Virtual Assistant
Handles settings, model paths, and performance parameters
"""

import os
import json
import psutil
from pathlib import Path
from typing import Dict, Any

class Config:
    """Central configuration management for the virtual assistant"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.config_file = self.base_dir / "data" / "config.json"
        self.models_dir = self.base_dir / "models"
        self.data_dir = self.base_dir / "data"
        
        # Ensure directories exist
        self.models_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # Detect system capabilities
        self.system_info = self._detect_system_capabilities()
        
        # Load or create configuration
        self.settings = self._load_config()
    
    def _detect_system_capabilities(self) -> Dict[str, Any]:
        """Detect system hardware capabilities for optimization"""
        memory_gb = psutil.virtual_memory().total / (1024**3)
        cpu_count = psutil.cpu_count()
        
        # Categorize system performance tier
        if memory_gb >= 16 and cpu_count >= 6:
            tier = "high_end"
        elif memory_gb >= 8 and cpu_count >= 4:
            tier = "mid_range"
        else:
            tier = "low_end"
        
        return {
            "memory_gb": memory_gb,
            "cpu_count": cpu_count,
            "performance_tier": tier
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration based on system capabilities"""
        tier = self.system_info["performance_tier"]
        
        base_config = {
            "speech": {
                "wake_word": "hey buddy",
                "language": "en-US",
                "sample_rate": 16000,
                "chunk_size": 1024
            },
            "nlp": {
                "spacy_model": "en_core_web_sm",
                "confidence_threshold": 0.7,
                "context_window": 5
            },
            "performance": {
                "max_memory_mb": 1500,
                "max_cpu_percent": 50,
                "cleanup_interval": 600,  # 10 minutes
                "model_cache_timeout": 300  # 5 minutes
            },
            "features": {
                "offline_mode": True,
                "auto_reminders": True,
                "voice_feedback": True,
                "gui_enabled": True
            },
            "paths": {
                "vosk_model": str(self.models_dir / "vosk-model-small-en-us-0.15"),
                "database": str(self.data_dir / "assistant.db"),
                "logs": str(self.data_dir / "logs")
            }
        }
        
        # Adjust settings based on system tier
        if tier == "high_end":
            base_config["nlp"]["spacy_model"] = "en_core_web_lg"
            base_config["performance"]["max_memory_mb"] = 3000
            base_config["speech"]["chunk_size"] = 2048
        elif tier == "low_end":
            base_config["performance"]["max_memory_mb"] = 800
            base_config["performance"]["max_cpu_percent"] = 30
            base_config["nlp"]["context_window"] = 3
        
        return base_config
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # Create default config
        config = self._get_default_config()
        self.save_config(config)
        return config
    
    def save_config(self, config: Dict[str, Any] = None):
        """Save configuration to file"""
        if config is None:
            config = self.settings
        
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
    
    def get(self, key_path: str, default=None):
        """Get configuration value using dot notation (e.g., 'speech.wake_word')"""
        keys = key_path.split('.')
        value = self.settings
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.settings
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
        self.save_config()
    
    @property
    def is_high_performance(self) -> bool:
        """Check if system can handle high-performance features"""
        return self.system_info["performance_tier"] == "high_end"
    
    @property
    def is_low_performance(self) -> bool:
        """Check if system requires low-performance optimizations"""
        return self.system_info["performance_tier"] == "low_end"

# Global configuration instance
config = Config()

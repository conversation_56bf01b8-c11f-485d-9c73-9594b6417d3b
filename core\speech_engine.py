"""
Speech recognition and synthesis engine for the Virtual Assistant
Uses Vosk for offline speech recognition and pyttsx3 for text-to-speech
"""

import json
import queue
import threading
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any
import pyaudio
import pyttsx3
from utils.config import config
from utils.logger import logger
from utils.memory_monitor import memory_monitor

try:
    import vosk
    VOSK_AVAILABLE = True
except ImportError:
    VOSK_AVAILABLE = False
    logger.warning("Vosk not available - speech recognition disabled")

class SpeechEngine:
    """Handles speech recognition and text-to-speech synthesis"""
    
    def __init__(self):
        self.audio = None
        self.stream = None
        self.vosk_model = None
        self.recognizer = None
        self.tts_engine = None
        
        self.listening = False
        self.recognition_thread = None
        self.audio_queue = queue.Queue()
        
        # Callbacks
        self.speech_callback = None
        self.wake_word_callback = None
        
        # Initialize components
        self._init_audio()
        self._init_tts()
        if VOSK_AVAILABLE:
            self._init_vosk()
    
    def _init_audio(self):
        """Initialize PyAudio for microphone input"""
        try:
            self.audio = pyaudio.PyAudio()
            logger.info("Audio system initialized")
        except Exception as e:
            logger.error(f"Failed to initialize audio: {e}")
            self.audio = None
    
    def _init_tts(self):
        """Initialize text-to-speech engine"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure TTS settings
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Prefer female voice if available
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            # Set speech rate and volume
            self.tts_engine.setProperty('rate', 180)  # Words per minute
            self.tts_engine.setProperty('volume', 0.8)
            
            logger.info("Text-to-speech engine initialized")
        except Exception as e:
            logger.error(f"Failed to initialize TTS: {e}")
            self.tts_engine = None
    
    def _init_vosk(self):
        """Initialize Vosk speech recognition model"""
        if not VOSK_AVAILABLE:
            return
        
        try:
            model_path = config.get("paths.vosk_model")
            
            # Check if cached model exists
            cached_model = memory_monitor.get_cached_model("vosk")
            if cached_model:
                self.vosk_model = cached_model
                logger.debug("Using cached Vosk model")
            else:
                # Load model from disk
                if model_path and Path(model_path).exists():
                    self.vosk_model = vosk.Model(model_path)
                    memory_monitor.cache_model("vosk", self.vosk_model)
                    logger.info(f"Vosk model loaded from {model_path}")
                else:
                    logger.warning("Vosk model not found - downloading small model")
                    # You would implement model download here
                    return
            
            # Create recognizer
            sample_rate = config.get("speech.sample_rate", 16000)
            self.recognizer = vosk.KaldiRecognizer(self.vosk_model, sample_rate)
            
        except Exception as e:
            logger.error(f"Failed to initialize Vosk: {e}")
            self.vosk_model = None
            self.recognizer = None
    
    def start_listening(self, callback: Callable[[str], None]):
        """Start continuous speech recognition"""
        if not self.audio or not self.recognizer:
            logger.error("Cannot start listening - audio or recognizer not available")
            return False
        
        if self.listening:
            logger.warning("Already listening")
            return True
        
        self.speech_callback = callback
        self.listening = True
        
        try:
            # Configure audio stream
            sample_rate = config.get("speech.sample_rate", 16000)
            chunk_size = config.get("speech.chunk_size", 1024)
            
            self.stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=sample_rate,
                input=True,
                frames_per_buffer=chunk_size
            )
            
            # Start recognition thread
            self.recognition_thread = threading.Thread(
                target=self._recognition_loop,
                daemon=True
            )
            self.recognition_thread.start()
            
            logger.info("Speech recognition started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start listening: {e}")
            self.listening = False
            return False
    
    def stop_listening(self):
        """Stop speech recognition"""
        if not self.listening:
            return
        
        self.listening = False
        
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
        
        if self.recognition_thread:
            self.recognition_thread.join(timeout=2)
        
        logger.info("Speech recognition stopped")
    
    def _recognition_loop(self):
        """Main speech recognition loop"""
        while self.listening and self.stream:
            try:
                # Read audio data
                data = self.stream.read(1024, exception_on_overflow=False)
                
                # Process with Vosk
                if self.recognizer.AcceptWaveform(data):
                    result = json.loads(self.recognizer.Result())
                    text = result.get('text', '').strip()
                    
                    if text and self.speech_callback:
                        logger.debug(f"Recognized speech: {text}")
                        self.speech_callback(text)
                
            except Exception as e:
                logger.error(f"Error in recognition loop: {e}")
                time.sleep(0.1)
    
    def speak(self, text: str, blocking: bool = False):
        """Convert text to speech"""
        if not self.tts_engine:
            logger.warning("TTS engine not available")
            return
        
        if not text.strip():
            return
        
        try:
            logger.debug(f"Speaking: {text}")
            
            if blocking:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            else:
                # Non-blocking speech in separate thread
                def speak_async():
                    self.tts_engine.say(text)
                    self.tts_engine.runAndWait()
                
                threading.Thread(target=speak_async, daemon=True).start()
                
        except Exception as e:
            logger.error(f"Error in text-to-speech: {e}")
    
    def recognize_once(self, timeout: float = 5.0) -> Optional[str]:
        """Recognize speech for a limited time"""
        if not self.audio or not self.recognizer:
            return None
        
        result_text = None
        recognition_complete = threading.Event()
        
        def callback(text):
            nonlocal result_text
            result_text = text
            recognition_complete.set()
        
        # Start temporary listening
        old_callback = self.speech_callback
        was_listening = self.listening
        
        if not was_listening:
            self.start_listening(callback)
        else:
            self.speech_callback = callback
        
        # Wait for result or timeout
        recognition_complete.wait(timeout)
        
        # Restore previous state
        if not was_listening:
            self.stop_listening()
        else:
            self.speech_callback = old_callback
        
        return result_text
    
    def is_wake_word(self, text: str) -> bool:
        """Check if text contains the wake word"""
        wake_word = config.get("speech.wake_word", "hey buddy").lower()
        return wake_word in text.lower()
    
    def get_audio_devices(self) -> Dict[str, Any]:
        """Get available audio input devices"""
        if not self.audio:
            return {}
        
        devices = {}
        try:
            for i in range(self.audio.get_device_count()):
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    devices[i] = {
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': device_info['defaultSampleRate']
                    }
        except Exception as e:
            logger.error(f"Error getting audio devices: {e}")
        
        return devices
    
    def cleanup(self):
        """Clean up resources"""
        self.stop_listening()
        
        if self.tts_engine:
            try:
                self.tts_engine.stop()
            except:
                pass
        
        if self.audio:
            self.audio.terminate()
        
        logger.info("Speech engine cleaned up")

# Global speech engine instance
speech_engine = SpeechEngine()

"""
Natural Language Processing for the Virtual Assistant
Handles intent classification, entity extraction, and context management
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from utils.config import config
from utils.logger import logger
from utils.memory_monitor import memory_monitor

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logger.warning("spaCy not available - using basic NLP")

@dataclass
class Intent:
    """Represents a detected intent with confidence score"""
    name: str
    confidence: float
    entities: Dict[str, Any]
    raw_text: str

@dataclass
class Entity:
    """Represents an extracted entity"""
    text: str
    label: str
    start: int
    end: int
    confidence: float = 1.0

class NLPProcessor:
    """Natural language processing engine for intent recognition"""
    
    def __init__(self):
        self.nlp_model = None
        self.intent_patterns = self._load_intent_patterns()
        self.context_window = config.get("nlp.context_window", 5)
        self.conversation_history = []
        
        if SPACY_AVAILABLE:
            self._init_spacy()
    
    def _init_spacy(self):
        """Initialize spaCy NLP model"""
        try:
            model_name = config.get("nlp.spacy_model", "en_core_web_sm")
            
            # Check if cached model exists
            cached_model = memory_monitor.get_cached_model(f"spacy_{model_name}")
            if cached_model:
                self.nlp_model = cached_model
                logger.debug(f"Using cached spaCy model: {model_name}")
            else:
                # Load model from disk
                self.nlp_model = spacy.load(model_name)
                memory_monitor.cache_model(f"spacy_{model_name}", self.nlp_model)
                logger.info(f"spaCy model loaded: {model_name}")
                
        except OSError:
            logger.warning(f"spaCy model {model_name} not found - using basic NLP")
            self.nlp_model = None
        except Exception as e:
            logger.error(f"Failed to load spaCy model: {e}")
            self.nlp_model = None
    
    def _load_intent_patterns(self) -> Dict[str, List[str]]:
        """Load intent recognition patterns"""
        return {
            "greeting": [
                r"hello|hi|hey|good morning|good afternoon|good evening",
                r"how are you|what's up|how's it going"
            ],
            "task_create": [
                r"remind me to|set a reminder|add task|create task|schedule",
                r"don't forget|make sure I|need to remember"
            ],
            "task_list": [
                r"what are my tasks|show my tasks|list tasks|what do I need to do",
                r"what's on my schedule|what's planned|my reminders"
            ],
            "task_complete": [
                r"mark.*complete|finished.*task|done with|completed",
                r"task.*done|finished.*reminder"
            ],
            "search_web": [
                r"search for|look up|find information|google|search the web",
                r"what is|who is|how to|where is"
            ],
            "send_message": [
                r"send message|text|whatsapp|message.*to",
                r"tell.*that|let.*know|send.*text"
            ],
            "read_email": [
                r"check email|read email|any new emails|email inbox",
                r"show me.*email|what emails"
            ],
            "send_email": [
                r"send email|email.*to|compose email|write email",
                r"email.*about|send.*email"
            ],
            "file_operation": [
                r"open file|find file|create file|delete file|move file",
                r"show me.*file|where is.*file|file.*location"
            ],
            "idea_suggest": [
                r"give me ideas|suggest|brainstorm|what should I",
                r"help me think|creative ideas|suggestions"
            ],
            "system_info": [
                r"system status|memory usage|performance|how are you running",
                r"system information|resource usage"
            ],
            "goodbye": [
                r"goodbye|bye|see you|talk to you later|exit|quit",
                r"that's all|thank you|thanks"
            ]
        }
    
    def process_text(self, text: str) -> Intent:
        """Process text and return detected intent with entities"""
        text = text.strip().lower()
        
        # Add to conversation history
        self.conversation_history.append(text)
        if len(self.conversation_history) > self.context_window:
            self.conversation_history.pop(0)
        
        # Extract entities
        entities = self._extract_entities(text)
        
        # Detect intent
        intent_name, confidence = self._detect_intent(text)
        
        # Enhance with context
        intent_name, confidence = self._apply_context(intent_name, confidence, text)
        
        return Intent(
            name=intent_name,
            confidence=confidence,
            entities=entities,
            raw_text=text
        )
    
    def _detect_intent(self, text: str) -> Tuple[str, float]:
        """Detect intent using pattern matching"""
        best_intent = "unknown"
        best_confidence = 0.0
        
        for intent_name, patterns in self.intent_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    # Calculate confidence based on match quality
                    match_length = len(match.group())
                    text_length = len(text)
                    confidence = min(0.9, match_length / text_length + 0.3)
                    
                    if confidence > best_confidence:
                        best_intent = intent_name
                        best_confidence = confidence
        
        # Apply minimum confidence threshold
        min_confidence = config.get("nlp.confidence_threshold", 0.7)
        if best_confidence < min_confidence:
            best_intent = "unknown"
            best_confidence = 0.0
        
        return best_intent, best_confidence
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities from text"""
        entities = {}
        
        if self.nlp_model:
            # Use spaCy for entity extraction
            doc = self.nlp_model(text)
            
            for ent in doc.ents:
                entity_type = ent.label_.lower()
                if entity_type not in entities:
                    entities[entity_type] = []
                
                entities[entity_type].append(Entity(
                    text=ent.text,
                    label=ent.label_,
                    start=ent.start_char,
                    end=ent.end_char,
                    confidence=1.0  # spaCy doesn't provide confidence scores
                ))
        
        # Extract common patterns manually
        entities.update(self._extract_manual_entities(text))
        
        return entities
    
    def _extract_manual_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities using manual patterns"""
        entities = {}
        
        # Time expressions
        time_patterns = [
            r"(\d{1,2}:\d{2}(?:\s*(?:am|pm))?)",
            r"(tomorrow|today|yesterday|next week|next month)",
            r"(in \d+ (?:minutes?|hours?|days?))",
            r"(at \d{1,2}(?::\d{2})?(?:\s*(?:am|pm))?)"
        ]
        
        for pattern in time_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if "time" not in entities:
                    entities["time"] = []
                entities["time"].append(Entity(
                    text=match.group(1),
                    label="TIME",
                    start=match.start(1),
                    end=match.end(1)
                ))
        
        # Email addresses
        email_pattern = r"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"
        matches = re.finditer(email_pattern, text)
        for match in matches:
            if "email" not in entities:
                entities["email"] = []
            entities["email"].append(Entity(
                text=match.group(1),
                label="EMAIL",
                start=match.start(1),
                end=match.end(1)
            ))
        
        # Phone numbers
        phone_pattern = r"(\+?\d{1,3}[-.\s]?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})"
        matches = re.finditer(phone_pattern, text)
        for match in matches:
            if "phone" not in entities:
                entities["phone"] = []
            entities["phone"].append(Entity(
                text=match.group(1),
                label="PHONE",
                start=match.start(1),
                end=match.end(1)
            ))
        
        return entities
    
    def _apply_context(self, intent: str, confidence: float, text: str) -> Tuple[str, float]:
        """Apply conversation context to improve intent detection"""
        if len(self.conversation_history) < 2:
            return intent, confidence
        
        # Look for context clues in recent conversation
        recent_context = " ".join(self.conversation_history[-3:])
        
        # Context-based intent adjustments
        context_adjustments = {
            "task_create": ["remind", "task", "schedule"],
            "search_web": ["search", "find", "look up"],
            "send_message": ["message", "text", "send"],
            "file_operation": ["file", "open", "save"]
        }
        
        for context_intent, keywords in context_adjustments.items():
            if any(keyword in recent_context for keyword in keywords):
                if intent == "unknown" or confidence < 0.5:
                    # Check if current text could match this context
                    if any(keyword in text for keyword in keywords):
                        return context_intent, min(0.8, confidence + 0.3)
        
        return intent, confidence
    
    def get_conversation_summary(self) -> str:
        """Get a summary of recent conversation"""
        if not self.conversation_history:
            return "No recent conversation"
        
        return " | ".join(self.conversation_history[-3:])
    
    def clear_context(self):
        """Clear conversation history"""
        self.conversation_history.clear()
        logger.debug("Conversation context cleared")
    
    def add_custom_intent(self, intent_name: str, patterns: List[str]):
        """Add custom intent patterns"""
        self.intent_patterns[intent_name] = patterns
        logger.info(f"Added custom intent: {intent_name}")
    
    def get_supported_intents(self) -> List[str]:
        """Get list of supported intent names"""
        return list(self.intent_patterns.keys())

# Global NLP processor instance
nlp_processor = NLPProcessor()

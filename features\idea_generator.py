"""
Idea generation system for the Virtual Assistant
Provides creative suggestions and brainstorming assistance
"""

import random
from typing import List, Dict, Any, Optional
from datetime import datetime
from utils.config import config
from utils.logger import logger
from core.context_manager import context_manager

class IdeaGenerator:
    """Generates ideas and suggestions for various contexts"""
    
    def __init__(self):
        self.idea_templates = self._load_idea_templates()
        self.context_keywords = self._load_context_keywords()
        self.suggestion_history = []
    
    def _load_idea_templates(self) -> Dict[str, List[str]]:
        """Load idea generation templates"""
        return {
            'productivity': [
                "Try the Pomodoro Technique: work for 25 minutes, then take a 5-minute break",
                "Create a morning routine that includes {activity} to start your day positively",
                "Use the 2-minute rule: if something takes less than 2 minutes, do it now",
                "Batch similar tasks together to improve efficiency",
                "Set up a dedicated workspace to improve focus",
                "Try time-blocking: assign specific time slots to different activities",
                "Use the Eisenhower Matrix to prioritize tasks by urgency and importance",
                "Implement a weekly review to assess progress and plan ahead"
            ],
            'creativity': [
                "Try mind mapping to explore connections between ideas",
                "Use the SCAMPER technique: Substitute, Combine, Adapt, Modify, Put to other use, Eliminate, Reverse",
                "Take a walk in nature to stimulate creative thinking",
                "Try free writing for 10 minutes without stopping",
                "Look at your problem from a different perspective or role",
                "Combine two unrelated concepts to create something new",
                "Use constraints to force creative solutions",
                "Try the 'What if?' game to explore possibilities"
            ],
            'learning': [
                "Use the Feynman Technique: explain concepts in simple terms",
                "Create flashcards for key concepts and review them regularly",
                "Teach someone else what you've learned",
                "Find real-world applications for theoretical concepts",
                "Join online communities related to your learning topic",
                "Set up a study schedule with regular review sessions",
                "Use multiple learning modalities: visual, auditory, kinesthetic",
                "Connect new information to what you already know"
            ],
            'health': [
                "Try a 10-minute morning stretch routine",
                "Drink a glass of water first thing in the morning",
                "Take the stairs instead of the elevator",
                "Practice deep breathing exercises for stress relief",
                "Go for a short walk during lunch break",
                "Try meal prepping on Sundays for the week",
                "Set a reminder to stand and move every hour",
                "Practice gratitude by writing down 3 things you're thankful for"
            ],
            'career': [
                "Update your LinkedIn profile with recent achievements",
                "Reach out to a mentor or industry contact",
                "Learn a new skill relevant to your field",
                "Volunteer for a challenging project at work",
                "Attend a virtual conference or webinar in your industry",
                "Start a side project to develop new skills",
                "Ask for feedback from colleagues or supervisors",
                "Set up informational interviews with people in roles you're interested in"
            ],
            'personal': [
                "Start a daily journaling practice",
                "Learn a new hobby or skill",
                "Reconnect with an old friend",
                "Declutter one area of your home",
                "Try cooking a new recipe",
                "Read a book outside your usual genre",
                "Practice a random act of kindness",
                "Set a personal challenge for the month"
            ]
        }
    
    def _load_context_keywords(self) -> Dict[str, List[str]]:
        """Load keywords for context detection"""
        return {
            'work': ['work', 'job', 'career', 'office', 'meeting', 'project', 'deadline', 'productivity'],
            'study': ['study', 'learn', 'education', 'school', 'exam', 'research', 'knowledge'],
            'health': ['health', 'fitness', 'exercise', 'diet', 'wellness', 'stress', 'sleep'],
            'creative': ['creative', 'art', 'design', 'writing', 'music', 'innovation', 'brainstorm'],
            'personal': ['personal', 'life', 'relationship', 'family', 'hobby', 'fun', 'leisure']
        }
    
    def generate_ideas(self, category: str = None, context: str = "", 
                      num_ideas: int = 3) -> List[str]:
        """Generate ideas based on category and context"""
        if category is None:
            category = self._detect_category(context)
        
        if category not in self.idea_templates:
            category = 'personal'  # Default fallback
        
        # Get ideas from the category
        available_ideas = self.idea_templates[category].copy()
        
        # Remove recently suggested ideas to avoid repetition
        recent_suggestions = [item['idea'] for item in self.suggestion_history[-10:]]
        available_ideas = [idea for idea in available_ideas if idea not in recent_suggestions]
        
        # If we don't have enough unique ideas, add some back
        if len(available_ideas) < num_ideas:
            available_ideas.extend(self.idea_templates[category])
        
        # Select random ideas
        selected_ideas = random.sample(available_ideas, min(num_ideas, len(available_ideas)))
        
        # Personalize ideas based on context
        personalized_ideas = []
        for idea in selected_ideas:
            personalized_idea = self._personalize_idea(idea, context)
            personalized_ideas.append(personalized_idea)
            
            # Log suggestion
            self.suggestion_history.append({
                'idea': personalized_idea,
                'category': category,
                'context': context,
                'timestamp': datetime.now()
            })
        
        # Keep history manageable
        if len(self.suggestion_history) > 100:
            self.suggestion_history = self.suggestion_history[-50:]
        
        logger.info(f"Generated {len(personalized_ideas)} ideas for category: {category}")
        return personalized_ideas
    
    def _detect_category(self, context: str) -> str:
        """Detect the most relevant category based on context"""
        context_lower = context.lower()
        
        category_scores = {}
        
        for category, keywords in self.context_keywords.items():
            score = sum(1 for keyword in keywords if keyword in context_lower)
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            # Return category with highest score
            best_category = max(category_scores, key=category_scores.get)
            
            # Map detected categories to template categories
            category_mapping = {
                'work': 'productivity',
                'study': 'learning',
                'health': 'health',
                'creative': 'creativity',
                'personal': 'personal'
            }
            
            return category_mapping.get(best_category, 'personal')
        
        return 'personal'  # Default
    
    def _personalize_idea(self, idea: str, context: str) -> str:
        """Personalize an idea based on context"""
        # Simple template replacement
        if '{activity}' in idea:
            activities = ['meditation', 'exercise', 'reading', 'planning', 'reflection']
            activity = random.choice(activities)
            idea = idea.replace('{activity}', activity)
        
        # Add context-specific modifications
        if 'work' in context.lower() and 'productivity' in idea:
            idea += " This can help improve your work efficiency."
        elif 'stress' in context.lower() and any(word in idea for word in ['break', 'walk', 'breathing']):
            idea += " This is especially helpful for managing stress."
        
        return idea
    
    def get_daily_suggestion(self) -> str:
        """Get a daily suggestion based on user preferences and history"""
        # Check user preferences for preferred categories
        preferred_categories = context_manager.get_preference('idea_categories', ['productivity', 'health', 'personal'])
        
        # Select a random category from preferences
        category = random.choice(preferred_categories)
        
        # Get current day context
        day_of_week = datetime.now().strftime('%A')
        context = f"It's {day_of_week}"
        
        ideas = self.generate_ideas(category=category, context=context, num_ideas=1)
        return ideas[0] if ideas else "Try something new today!"
    
    def suggest_based_on_task(self, task_description: str) -> List[str]:
        """Suggest ideas based on a specific task"""
        # Analyze task for keywords
        task_lower = task_description.lower()
        
        suggestions = []
        
        # Task-specific suggestions
        if any(word in task_lower for word in ['write', 'writing', 'document']):
            suggestions.extend([
                "Break your writing into small, manageable sections",
                "Start with an outline to organize your thoughts",
                "Use the Pomodoro Technique: write for 25 minutes, then take a break"
            ])
        
        elif any(word in task_lower for word in ['study', 'learn', 'research']):
            suggestions.extend([
                "Use active recall: test yourself on the material",
                "Create visual aids like diagrams or mind maps",
                "Teach the concept to someone else or explain it out loud"
            ])
        
        elif any(word in task_lower for word in ['meeting', 'presentation']):
            suggestions.extend([
                "Prepare key points in advance and practice them",
                "Arrive a few minutes early to set up and get comfortable",
                "Use the STAR method for answering questions (Situation, Task, Action, Result)"
            ])
        
        elif any(word in task_lower for word in ['exercise', 'workout', 'fitness']):
            suggestions.extend([
                "Start with a 5-minute warm-up to prevent injury",
                "Set a specific, achievable goal for today's session",
                "Listen to energizing music to stay motivated"
            ])
        
        else:
            # General task suggestions
            suggestions.extend([
                "Break the task into smaller, actionable steps",
                "Set a timer and work in focused bursts",
                "Eliminate distractions before starting"
            ])
        
        return suggestions[:3]  # Return top 3 suggestions
    
    def get_brainstorming_prompts(self, topic: str) -> List[str]:
        """Generate brainstorming prompts for a given topic"""
        prompts = [
            f"What if {topic} didn't exist? How would things be different?",
            f"How might we improve {topic} for future generations?",
            f"What would {topic} look like if it were designed by a child?",
            f"How could we combine {topic} with something completely unrelated?",
            f"What are 10 unusual uses for {topic}?",
            f"If {topic} were a person, what would their personality be like?",
            f"What would happen if {topic} were 10 times bigger/smaller?",
            f"How would someone from 100 years ago approach {topic}?"
        ]
        
        return random.sample(prompts, min(5, len(prompts)))
    
    def get_suggestion_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent suggestion history"""
        return self.suggestion_history[-limit:]
    
    def set_preferred_categories(self, categories: List[str]):
        """Set user's preferred idea categories"""
        valid_categories = list(self.idea_templates.keys())
        filtered_categories = [cat for cat in categories if cat in valid_categories]
        
        if filtered_categories:
            context_manager.set_preference('idea_categories', filtered_categories, 'ideas')
            logger.info(f"Set preferred idea categories: {filtered_categories}")
        else:
            logger.warning("No valid categories provided")
    
    def get_available_categories(self) -> List[str]:
        """Get list of available idea categories"""
        return list(self.idea_templates.keys())

# Global idea generator instance
idea_generator = IdeaGenerator()

"""
Web search functionality for the Virtual Assistant
Handles internet searches and content extraction
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import quote_plus, urljoin, urlparse
from typing import List, Dict, Any, Optional
import time
import re
from utils.config import config
from utils.logger import logger

class SearchResult:
    """Represents a search result"""
    
    def __init__(self, title: str, url: str, snippet: str, source: str = ""):
        self.title = title
        self.url = url
        self.snippet = snippet
        self.source = source

class WebSearcher:
    """Handles web searches and content extraction"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.timeout = 10
        self.max_results = 10
    
    def search(self, query: str, num_results: int = 5) -> List[SearchResult]:
        """Perform web search using multiple search engines"""
        results = []
        
        # Try DuckDuckGo first (privacy-focused)
        try:
            ddg_results = self._search_duckduckgo(query, num_results)
            results.extend(ddg_results)
        except Exception as e:
            logger.warning(f"DuckDuckGo search failed: {e}")
        
        # If not enough results, try other methods
        if len(results) < num_results:
            try:
                bing_results = self._search_bing(query, num_results - len(results))
                results.extend(bing_results)
            except Exception as e:
                logger.warning(f"Bing search failed: {e}")
        
        # Remove duplicates and limit results
        unique_results = self._remove_duplicates(results)
        return unique_results[:num_results]
    
    def _search_duckduckgo(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using DuckDuckGo"""
        results = []
        
        try:
            # DuckDuckGo instant answer API
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # Extract instant answer
            if data.get('Abstract'):
                results.append(SearchResult(
                    title=data.get('Heading', query),
                    url=data.get('AbstractURL', ''),
                    snippet=data.get('Abstract', ''),
                    source='DuckDuckGo'
                ))
            
            # Extract related topics
            for topic in data.get('RelatedTopics', [])[:num_results-1]:
                if isinstance(topic, dict) and 'Text' in topic:
                    results.append(SearchResult(
                        title=topic.get('Text', '').split(' - ')[0],
                        url=topic.get('FirstURL', ''),
                        snippet=topic.get('Text', ''),
                        source='DuckDuckGo'
                    ))
            
        except Exception as e:
            logger.error(f"DuckDuckGo search error: {e}")
        
        return results
    
    def _search_bing(self, query: str, num_results: int) -> List[SearchResult]:
        """Search using Bing (web scraping)"""
        results = []
        
        try:
            url = f"https://www.bing.com/search?q={quote_plus(query)}"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find search result containers
            search_results = soup.find_all('li', class_='b_algo')
            
            for result in search_results[:num_results]:
                try:
                    # Extract title and URL
                    title_elem = result.find('h2')
                    if not title_elem:
                        continue
                    
                    link_elem = title_elem.find('a')
                    if not link_elem:
                        continue
                    
                    title = link_elem.get_text(strip=True)
                    url = link_elem.get('href', '')
                    
                    # Extract snippet
                    snippet_elem = result.find('p') or result.find('div', class_='b_caption')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                    
                    if title and url:
                        results.append(SearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            source='Bing'
                        ))
                
                except Exception as e:
                    logger.debug(f"Error parsing Bing result: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Bing search error: {e}")
        
        return results
    
    def _remove_duplicates(self, results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate search results"""
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        return unique_results
    
    def extract_content(self, url: str) -> Dict[str, Any]:
        """Extract content from a webpage"""
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract title
            title = ""
            title_elem = soup.find('title')
            if title_elem:
                title = title_elem.get_text(strip=True)
            
            # Extract main content
            content = ""
            
            # Try to find main content areas
            main_selectors = [
                'main', 'article', '.content', '#content',
                '.post-content', '.entry-content', '.article-content'
            ]
            
            for selector in main_selectors:
                main_elem = soup.select_one(selector)
                if main_elem:
                    content = main_elem.get_text(separator=' ', strip=True)
                    break
            
            # Fallback to body content
            if not content:
                body = soup.find('body')
                if body:
                    content = body.get_text(separator=' ', strip=True)
            
            # Clean up content
            content = re.sub(r'\s+', ' ', content)
            content = content[:2000]  # Limit content length
            
            # Extract meta description
            description = ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content', '')
            
            return {
                'title': title,
                'content': content,
                'description': description,
                'url': url,
                'success': True
            }
        
        except Exception as e:
            logger.error(f"Content extraction error for {url}: {e}")
            return {
                'title': '',
                'content': '',
                'description': '',
                'url': url,
                'success': False,
                'error': str(e)
            }
    
    def quick_answer(self, query: str) -> Optional[str]:
        """Get a quick answer for simple queries"""
        try:
            # Try DuckDuckGo instant answer
            url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # Return abstract if available
            if data.get('Abstract'):
                return data['Abstract']
            
            # Return answer if available
            if data.get('Answer'):
                return data['Answer']
            
            # Return definition if available
            if data.get('Definition'):
                return data['Definition']
            
        except Exception as e:
            logger.error(f"Quick answer error: {e}")
        
        return None
    
    def search_news(self, query: str, num_results: int = 5) -> List[SearchResult]:
        """Search for news articles"""
        results = []
        
        try:
            # Search with news-specific query
            news_query = f"{query} news"
            url = f"https://www.bing.com/news/search?q={quote_plus(news_query)}"
            
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find news result containers
            news_results = soup.find_all('div', class_='news-card')
            
            for result in news_results[:num_results]:
                try:
                    title_elem = result.find('a')
                    if not title_elem:
                        continue
                    
                    title = title_elem.get_text(strip=True)
                    url = title_elem.get('href', '')
                    
                    snippet_elem = result.find('p')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                    
                    if title and url:
                        results.append(SearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            source='Bing News'
                        ))
                
                except Exception as e:
                    logger.debug(f"Error parsing news result: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"News search error: {e}")
        
        return results
    
    def is_url_accessible(self, url: str) -> bool:
        """Check if a URL is accessible"""
        try:
            response = self.session.head(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_search_suggestions(self, query: str) -> List[str]:
        """Get search suggestions for a query"""
        suggestions = []
        
        try:
            # Use Bing autosuggest API
            url = "https://api.bing.microsoft.com/osjson.aspx"
            params = {'query': query}
            
            response = self.session.get(url, params=params, timeout=5)
            response.raise_for_status()
            
            data = response.json()
            if len(data) > 1:
                suggestions = data[1][:5]  # Limit to 5 suggestions
        
        except Exception as e:
            logger.debug(f"Search suggestions error: {e}")
        
        return suggestions

# Global web searcher instance
web_searcher = WebSearcher()

{"speech": {"wake_word": "hey buddy", "language": "en-US", "sample_rate": 16000, "chunk_size": 1024}, "nlp": {"spacy_model": "en_core_web_sm", "confidence_threshold": 0.7, "context_window": 5}, "performance": {"max_memory_mb": 1500, "max_cpu_percent": 50, "cleanup_interval": 600, "model_cache_timeout": 300}, "features": {"offline_mode": true, "auto_reminders": true, "voice_feedback": true, "gui_enabled": true}, "paths": {"vosk_model": "D:\\py-program\\App\\buddy\\models\\vosk-model-small-en-us-0.15", "database": "D:\\py-program\\App\\buddy\\data\\assistant.db", "logs": "D:\\py-program\\App\\buddy\\data\\logs"}}
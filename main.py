"""
Virtual Assistant - Main Application
CPU-optimized virtual assistant with speech recognition, task management, and external integrations
"""

import sys
import signal
import threading
import time
from typing import Dict, Any
from utils.config import config
from utils.logger import logger
from utils.memory_monitor import memory_monitor
from core.speech_engine import speech_engine
from core.nlp_processor import nlp_processor
from core.context_manager import context_manager
from core.task_scheduler import task_scheduler
from features.task_manager import task_manager
from features.web_searcher import web_searcher
from features.whatsapp_bot import whatsapp_bot
from features.email_client import email_client
from features.file_manager import file_manager
from features.idea_generator import idea_generator

class VirtualAssistant:
    """Main Virtual Assistant application"""
    
    def __init__(self):
        self.running = False
        self.listening = False
        self.wake_word_detected = False
        
        # Initialize components
        self._initialize_components()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _initialize_components(self):
        """Initialize all assistant components"""
        logger.info("Initializing Virtual Assistant...")
        
        # Start memory monitoring
        memory_monitor.start_monitoring()
        
        # Start task scheduler
        task_scheduler.start()
        
        # Log system information
        system_info = config.system_info
        logger.info(f"System: {system_info['performance_tier']} tier "
                   f"({system_info['memory_gb']:.1f}GB RAM, {system_info['cpu_count']} CPUs)")
        
        logger.info("Virtual Assistant initialized successfully")
    
    def start(self):
        """Start the virtual assistant"""
        if self.running:
            logger.warning("Assistant is already running")
            return
        
        self.running = True
        logger.info("Starting Virtual Assistant...")
        
        # Speak welcome message
        speech_engine.speak("Hello! I'm your virtual assistant. Say 'Hey Buddy' to wake me up.")
        
        # Start listening for wake word
        self._start_wake_word_detection()
        
        # Main loop
        try:
            self._main_loop()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the virtual assistant"""
        if not self.running:
            return
        
        logger.info("Stopping Virtual Assistant...")
        self.running = False
        
        # Stop speech recognition
        speech_engine.stop_listening()
        
        # Stop task scheduler
        task_scheduler.stop()
        
        # Stop memory monitoring
        memory_monitor.stop_monitoring()
        
        # End current session
        context_manager.end_current_session()
        
        # Cleanup resources
        speech_engine.cleanup()
        
        logger.info("Virtual Assistant stopped")
    
    def _start_wake_word_detection(self):
        """Start listening for wake word"""
        def wake_word_callback(text):
            if speech_engine.is_wake_word(text):
                self.wake_word_detected = True
                logger.info("Wake word detected!")
                speech_engine.speak("Yes, how can I help you?")
                
                # Listen for command
                self._listen_for_command()
        
        speech_engine.start_listening(wake_word_callback)
        self.listening = True
        logger.info("Listening for wake word...")
    
    def _listen_for_command(self):
        """Listen for user command after wake word"""
        def command_callback(text):
            if text.strip():
                self._process_command(text)
                self.wake_word_detected = False
        
        # Listen for command with timeout
        threading.Thread(
            target=self._command_listener_thread,
            args=(command_callback,),
            daemon=True
        ).start()
    
    def _command_listener_thread(self, callback):
        """Thread for listening to commands with timeout"""
        start_time = time.time()
        timeout = 10  # 10 seconds to give command
        
        while time.time() - start_time < timeout and self.wake_word_detected:
            # This would be implemented with proper speech recognition
            # For now, we'll use a simple timeout mechanism
            time.sleep(0.1)
        
        if self.wake_word_detected:
            speech_engine.speak("I didn't hear anything. Say 'Hey Buddy' to try again.")
            self.wake_word_detected = False
    
    def _process_command(self, text: str):
        """Process user command"""
        logger.info(f"Processing command: {text}")
        
        # Process with NLP
        intent = nlp_processor.process_text(text)
        
        # Generate response
        response = self._handle_intent(intent)
        
        # Add to conversation context
        context_manager.add_conversation_turn(
            user_input=text,
            intent=intent.name,
            confidence=intent.confidence,
            assistant_response=response,
            entities=intent.entities
        )
        
        # Speak response
        if response:
            speech_engine.speak(response)
    
    def _handle_intent(self, intent) -> str:
        """Handle detected intent and return response"""
        intent_name = intent.name
        entities = intent.entities
        
        try:
            if intent_name == "greeting":
                return self._handle_greeting()
            
            elif intent_name == "task_create":
                return self._handle_task_create(intent.raw_text, entities)
            
            elif intent_name == "task_list":
                return self._handle_task_list()
            
            elif intent_name == "task_complete":
                return self._handle_task_complete(intent.raw_text)
            
            elif intent_name == "search_web":
                return self._handle_web_search(intent.raw_text)
            
            elif intent_name == "send_message":
                return self._handle_send_message(intent.raw_text)
            
            elif intent_name == "read_email":
                return self._handle_read_email()
            
            elif intent_name == "send_email":
                return self._handle_send_email(intent.raw_text)
            
            elif intent_name == "file_operation":
                return self._handle_file_operation(intent.raw_text)
            
            elif intent_name == "idea_suggest":
                return self._handle_idea_suggestion(intent.raw_text)
            
            elif intent_name == "system_info":
                return self._handle_system_info()
            
            elif intent_name == "goodbye":
                return self._handle_goodbye()
            
            else:
                return self._handle_unknown_intent(intent.raw_text)
        
        except Exception as e:
            logger.error(f"Error handling intent {intent_name}: {e}")
            return "I'm sorry, I encountered an error processing your request."
    
    def _handle_greeting(self) -> str:
        """Handle greeting intent"""
        greetings = [
            "Hello! How can I help you today?",
            "Hi there! What can I do for you?",
            "Good to see you! How may I assist you?",
            "Hello! I'm here to help. What do you need?"
        ]
        import random
        return random.choice(greetings)
    
    def _handle_task_create(self, text: str, entities: Dict) -> str:
        """Handle task creation"""
        # Extract task details from text
        task_text = text.replace("remind me to", "").replace("set a reminder", "").strip()
        
        # Create task
        task_id = task_manager.create_task(
            title=task_text,
            description=f"Created from voice command: {text}"
        )
        
        if task_id:
            return f"I've created a task: {task_text}"
        else:
            return "I couldn't create the task. Please try again."
    
    def _handle_task_list(self) -> str:
        """Handle task listing"""
        from features.task_manager import TaskStatus
        tasks = task_manager.get_tasks(status=TaskStatus.PENDING, limit=5)
        
        if not tasks:
            return "You don't have any pending tasks."
        
        task_list = "Here are your pending tasks: "
        for i, task in enumerate(tasks, 1):
            task_list += f"{i}. {task.title}. "
        
        return task_list
    
    def _handle_task_complete(self, text: str) -> str:
        """Handle task completion"""
        # This is a simplified implementation
        # In a real system, you'd need better task identification
        return "Please specify which task you'd like to mark as complete."
    
    def _handle_web_search(self, text: str) -> str:
        """Handle web search"""
        # Extract search query
        query = text.replace("search for", "").replace("look up", "").strip()
        
        # Perform search
        results = web_searcher.search(query, num_results=3)
        
        if results:
            response = f"I found {len(results)} results for '{query}': "
            for i, result in enumerate(results, 1):
                response += f"{i}. {result.title}. "
            return response
        else:
            return f"I couldn't find any results for '{query}'."
    
    def _handle_send_message(self, text: str) -> str:
        """Handle message sending"""
        # Parse message command
        command = whatsapp_bot.parse_message_command(text)
        
        if command:
            success = whatsapp_bot.execute_command(command)
            if success:
                return f"I'll send the message to {command['recipient']}."
            else:
                return "I couldn't send the message. Please check the recipient details."
        else:
            return "I couldn't understand the message details. Please try again."
    
    def _handle_read_email(self) -> str:
        """Handle email reading"""
        if not email_client._is_configured():
            return "Email is not configured. Please set up your email first."
        
        unread_count = email_client.get_unread_count()
        if unread_count > 0:
            return f"You have {unread_count} unread emails."
        else:
            return "You don't have any unread emails."
    
    def _handle_send_email(self, text: str) -> str:
        """Handle email sending"""
        return "Email sending feature is available. Please provide recipient and message details."
    
    def _handle_file_operation(self, text: str) -> str:
        """Handle file operations"""
        if "open" in text.lower():
            return "Please specify which file you'd like to open."
        elif "find" in text.lower():
            return "Please specify what file you're looking for."
        else:
            return "I can help you open, find, or manage files. What would you like to do?"
    
    def _handle_idea_suggestion(self, text: str) -> str:
        """Handle idea suggestions"""
        ideas = idea_generator.generate_ideas(context=text, num_ideas=2)
        
        if ideas:
            response = "Here are some ideas for you: "
            for i, idea in enumerate(ideas, 1):
                response += f"{i}. {idea}. "
            return response
        else:
            return "I'm having trouble generating ideas right now. Please try again."
    
    def _handle_system_info(self) -> str:
        """Handle system information requests"""
        stats = memory_monitor.get_performance_stats()
        return (f"System status: Using {stats['memory_mb']:.1f} MB of memory, "
                f"CPU at {stats['cpu_percent']:.1f}%. Everything is running smoothly.")
    
    def _handle_goodbye(self) -> str:
        """Handle goodbye intent"""
        return "Goodbye! Say 'Hey Buddy' anytime you need help."
    
    def _handle_unknown_intent(self, text: str) -> str:
        """Handle unknown intents"""
        return ("I'm not sure how to help with that. I can help you with tasks, "
                "web searches, messages, emails, files, and ideas. What would you like to do?")
    
    def _main_loop(self):
        """Main application loop"""
        logger.info("Virtual Assistant is running. Press Ctrl+C to stop.")
        
        while self.running:
            try:
                time.sleep(1)
                
                # Periodic maintenance
                if int(time.time()) % 300 == 0:  # Every 5 minutes
                    self._periodic_maintenance()
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(5)
    
    def _periodic_maintenance(self):
        """Perform periodic maintenance tasks"""
        # Log performance stats
        stats = memory_monitor.get_performance_stats()
        logger.debug(f"Performance: {stats['memory_mb']:.1f}MB memory, "
                    f"{stats['cpu_percent']:.1f}% CPU")
    
    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown"""
        logger.info(f"Received signal {signum}")
        self.stop()
        sys.exit(0)

def main():
    """Main entry point"""
    try:
        assistant = VirtualAssistant()
        assistant.start()
    except Exception as e:
        logger.error(f"Failed to start Virtual Assistant: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

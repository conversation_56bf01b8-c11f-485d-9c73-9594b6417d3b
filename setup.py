"""
Setup script for the Virtual Assistant
Handles installation and initial configuration
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error installing requirements: {e}")
        return False
    
    return True

def download_vosk_model():
    """Download Vosk speech recognition model"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    model_name = "vosk-model-small-en-us-0.15"
    model_path = models_dir / model_name
    
    if model_path.exists():
        print(f"✓ Vosk model already exists at {model_path}")
        return True
    
    print("Downloading Vosk speech recognition model...")
    model_url = f"https://alphacephei.com/vosk/models/{model_name}.zip"
    zip_path = models_dir / f"{model_name}.zip"
    
    try:
        # Download model
        print(f"Downloading from {model_url}")
        urllib.request.urlretrieve(model_url, zip_path)
        
        # Extract model
        print("Extracting model...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(models_dir)
        
        # Remove zip file
        zip_path.unlink()
        
        print(f"✓ Vosk model downloaded and extracted to {model_path}")
        return True
        
    except Exception as e:
        print(f"Error downloading Vosk model: {e}")
        print("You can manually download the model from:")
        print(f"  {model_url}")
        print(f"And extract it to: {model_path}")
        return False

def install_spacy_model():
    """Install spaCy language model"""
    print("Installing spaCy English model...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        print("✓ spaCy English model installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing spaCy model: {e}")
        print("You can manually install it with:")
        print("  python -m spacy download en_core_web_sm")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["data", "data/logs", "models"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def test_installation():
    """Test if installation was successful"""
    print("\nTesting installation...")
    
    try:
        # Test imports
        from utils.config import config
        from utils.logger import logger
        from core.speech_engine import speech_engine
        from core.nlp_processor import nlp_processor
        
        print("✓ Core modules imported successfully")
        
        # Test configuration
        system_info = config.system_info
        print(f"✓ System detected: {system_info['performance_tier']} tier")
        print(f"  Memory: {system_info['memory_gb']:.1f}GB")
        print(f"  CPUs: {system_info['cpu_count']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Installation test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("Virtual Assistant Setup")
    print("=" * 50)
    
    # Check Python version
    check_python_version()
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("Setup failed during requirements installation")
        sys.exit(1)
    
    # Install spaCy model
    install_spacy_model()
    
    # Download Vosk model
    download_vosk_model()
    
    # Test installation
    if test_installation():
        print("\n" + "=" * 50)
        print("✓ Setup completed successfully!")
        print("\nTo start the assistant:")
        print("  Command line: python main.py")
        print("  GUI interface: python gui.py")
        print("\nFor help and documentation, see README.md")
    else:
        print("\n" + "=" * 50)
        print("✗ Setup completed with errors")
        print("Please check the error messages above and try again")

if __name__ == "__main__":
    main()

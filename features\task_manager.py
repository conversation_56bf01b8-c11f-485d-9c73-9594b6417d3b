"""
Task management system for the Virtual Assistant
Handles task creation, scheduling, reminders, and completion tracking
"""

import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from utils.config import config
from utils.logger import logger
from core.task_scheduler import task_scheduler

class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4

@dataclass
class Task:
    """Represents a task with all its properties"""
    id: str
    title: str
    description: str
    status: TaskStatus
    priority: TaskPriority
    created_at: datetime
    due_date: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    reminder_time: Optional[datetime] = None
    tags: List[str] = None
    category: str = "general"
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class TaskManager:
    """Manages tasks, reminders, and scheduling"""
    
    def __init__(self):
        self.db_path = Path(config.get("paths.database"))
        self._init_database()
        
        # Set up reminder callback
        task_scheduler.set_reminder_callback(self._handle_reminder)
    
    def _init_database(self):
        """Initialize task database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS tasks (
                        id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        description TEXT,
                        status TEXT NOT NULL,
                        priority INTEGER NOT NULL,
                        created_at TEXT NOT NULL,
                        due_date TEXT,
                        completed_at TEXT,
                        reminder_time TEXT,
                        tags TEXT,
                        category TEXT DEFAULT 'general',
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create indexes
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_status ON tasks(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_due_date ON tasks(due_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON tasks(category)')
                
                conn.commit()
                logger.info("Task database initialized")
                
        except Exception as e:
            logger.error(f"Failed to initialize task database: {e}")
    
    def create_task(self, title: str, description: str = "", 
                   priority: TaskPriority = TaskPriority.MEDIUM,
                   due_date: Optional[datetime] = None,
                   reminder_time: Optional[datetime] = None,
                   tags: List[str] = None,
                   category: str = "general") -> str:
        """Create a new task"""
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        task = Task(
            id=task_id,
            title=title,
            description=description,
            status=TaskStatus.PENDING,
            priority=priority,
            created_at=datetime.now(),
            due_date=due_date,
            reminder_time=reminder_time,
            tags=tags or [],
            category=category
        )
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO tasks 
                    (id, title, description, status, priority, created_at, 
                     due_date, reminder_time, tags, category)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task.id,
                    task.title,
                    task.description,
                    task.status.value,
                    task.priority.value,
                    task.created_at.isoformat(),
                    task.due_date.isoformat() if task.due_date else None,
                    task.reminder_time.isoformat() if task.reminder_time else None,
                    ','.join(task.tags),
                    task.category
                ))
                conn.commit()
            
            # Schedule reminder if specified
            if reminder_time and reminder_time > datetime.now():
                task_scheduler.schedule_reminder(
                    task_id=task_id,
                    message=f"Reminder: {title}",
                    scheduled_time=reminder_time
                )
            
            logger.info(f"Created task: {title}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            return ""
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a specific task by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title, description, status, priority, created_at,
                           due_date, completed_at, reminder_time, tags, category
                    FROM tasks WHERE id = ?
                ''', (task_id,))
                
                row = cursor.fetchone()
                if row:
                    return self._row_to_task(row)
                
        except Exception as e:
            logger.error(f"Failed to get task: {e}")
        
        return None
    
    def get_tasks(self, status: Optional[TaskStatus] = None,
                 category: Optional[str] = None,
                 limit: int = 100) -> List[Task]:
        """Get tasks with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT id, title, description, status, priority, created_at,
                           due_date, completed_at, reminder_time, tags, category
                    FROM tasks
                '''
                params = []
                conditions = []
                
                if status:
                    conditions.append("status = ?")
                    params.append(status.value)
                
                if category:
                    conditions.append("category = ?")
                    params.append(category)
                
                if conditions:
                    query += " WHERE " + " AND ".join(conditions)
                
                query += " ORDER BY priority DESC, created_at DESC LIMIT ?"
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [self._row_to_task(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get tasks: {e}")
            return []
    
    def update_task_status(self, task_id: str, status: TaskStatus) -> bool:
        """Update task status"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                update_data = {
                    'status': status.value,
                    'updated_at': datetime.now().isoformat()
                }
                
                if status == TaskStatus.COMPLETED:
                    update_data['completed_at'] = datetime.now().isoformat()
                
                cursor.execute('''
                    UPDATE tasks 
                    SET status = ?, completed_at = ?, updated_at = ?
                    WHERE id = ?
                ''', (
                    update_data['status'],
                    update_data.get('completed_at'),
                    update_data['updated_at'],
                    task_id
                ))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    
                    # Cancel reminder if task is completed or cancelled
                    if status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
                        task_scheduler.cancel_task(task_id)
                    
                    logger.info(f"Updated task {task_id} status to {status.value}")
                    return True
                
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")
        
        return False
    
    def delete_task(self, task_id: str) -> bool:
        """Delete a task"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM tasks WHERE id = ?', (task_id,))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    
                    # Cancel any scheduled reminders
                    task_scheduler.cancel_task(task_id)
                    
                    logger.info(f"Deleted task: {task_id}")
                    return True
                
        except Exception as e:
            logger.error(f"Failed to delete task: {e}")
        
        return False
    
    def get_overdue_tasks(self) -> List[Task]:
        """Get tasks that are overdue"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title, description, status, priority, created_at,
                           due_date, completed_at, reminder_time, tags, category
                    FROM tasks
                    WHERE due_date < ? AND status NOT IN (?, ?)
                    ORDER BY due_date ASC
                ''', (
                    datetime.now().isoformat(),
                    TaskStatus.COMPLETED.value,
                    TaskStatus.CANCELLED.value
                ))
                
                rows = cursor.fetchall()
                return [self._row_to_task(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get overdue tasks: {e}")
            return []
    
    def get_upcoming_tasks(self, days_ahead: int = 7) -> List[Task]:
        """Get tasks due in the next specified days"""
        try:
            cutoff_date = datetime.now() + timedelta(days=days_ahead)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title, description, status, priority, created_at,
                           due_date, completed_at, reminder_time, tags, category
                    FROM tasks
                    WHERE due_date BETWEEN ? AND ? 
                    AND status NOT IN (?, ?)
                    ORDER BY due_date ASC
                ''', (
                    datetime.now().isoformat(),
                    cutoff_date.isoformat(),
                    TaskStatus.COMPLETED.value,
                    TaskStatus.CANCELLED.value
                ))
                
                rows = cursor.fetchall()
                return [self._row_to_task(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to get upcoming tasks: {e}")
            return []
    
    def search_tasks(self, query: str) -> List[Task]:
        """Search tasks by title or description"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, title, description, status, priority, created_at,
                           due_date, completed_at, reminder_time, tags, category
                    FROM tasks
                    WHERE title LIKE ? OR description LIKE ?
                    ORDER BY priority DESC, created_at DESC
                ''', (f'%{query}%', f'%{query}%'))
                
                rows = cursor.fetchall()
                return [self._row_to_task(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Failed to search tasks: {e}")
            return []
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """Get task statistics"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Count by status
                cursor.execute('''
                    SELECT status, COUNT(*) FROM tasks GROUP BY status
                ''')
                status_counts = dict(cursor.fetchall())
                
                # Count by priority
                cursor.execute('''
                    SELECT priority, COUNT(*) FROM tasks 
                    WHERE status NOT IN (?, ?) GROUP BY priority
                ''', (TaskStatus.COMPLETED.value, TaskStatus.CANCELLED.value))
                priority_counts = dict(cursor.fetchall())
                
                # Overdue count
                cursor.execute('''
                    SELECT COUNT(*) FROM tasks
                    WHERE due_date < ? AND status NOT IN (?, ?)
                ''', (
                    datetime.now().isoformat(),
                    TaskStatus.COMPLETED.value,
                    TaskStatus.CANCELLED.value
                ))
                overdue_count = cursor.fetchone()[0]
                
                return {
                    'total_tasks': sum(status_counts.values()),
                    'status_counts': status_counts,
                    'priority_counts': priority_counts,
                    'overdue_count': overdue_count
                }
                
        except Exception as e:
            logger.error(f"Failed to get task statistics: {e}")
            return {}
    
    def _row_to_task(self, row) -> Task:
        """Convert database row to Task object"""
        return Task(
            id=row[0],
            title=row[1],
            description=row[2] or "",
            status=TaskStatus(row[3]),
            priority=TaskPriority(row[4]),
            created_at=datetime.fromisoformat(row[5]),
            due_date=datetime.fromisoformat(row[6]) if row[6] else None,
            completed_at=datetime.fromisoformat(row[7]) if row[7] else None,
            reminder_time=datetime.fromisoformat(row[8]) if row[8] else None,
            tags=row[9].split(',') if row[9] else [],
            category=row[10] or "general"
        )
    
    def _handle_reminder(self, task_id: str, message: str):
        """Handle reminder notifications"""
        task = self.get_task(task_id)
        if task:
            logger.info(f"Task reminder: {task.title}")
            
            # You can add notification logic here
            # For example, show system notification or speak the reminder
            try:
                from core.speech_engine import speech_engine
                speech_engine.speak(f"Reminder: {task.title}")
            except Exception as e:
                logger.error(f"Failed to speak reminder: {e}")

# Global task manager instance
task_manager = TaskManager()

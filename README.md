# Virtual Assistant - Buddy

A comprehensive CPU-optimized virtual assistant that runs efficiently on standard laptops without requiring a graphics card (GPU). Features real-time conversation, task management, web search, WhatsApp integration, email management, file operations, and AI-powered idea suggestions.

## Features

### 🎤 **Speech Processing**
- Offline speech recognition using Vosk
- Text-to-speech synthesis with pyttsx3
- Wake word detection ("Hey Buddy")
- CPU-optimized for standard laptops

### 📋 **Task Management**
- Create, manage, and track tasks
- Set reminders with scheduling
- Task prioritization and categorization
- Persistent storage with SQLite

### 🌐 **Web Integration**
- Internet search with multiple search engines
- Content extraction from web pages
- Quick answers for simple queries
- News search functionality

### 📱 **Communication**
- WhatsApp message automation
- Email reading and sending
- Contact management
- Message scheduling

### 📁 **File Management**
- File operations (open, create, delete, move, copy)
- File search and organization
- Safe directory access controls
- System integration

### 💡 **Idea Generation**
- AI-powered creative suggestions
- Context-aware brainstorming
- Productivity tips and recommendations
- Personalized idea categories

## System Requirements

### Minimum Requirements
- **OS**: Windows 10/11, macOS 10.14+, or Linux
- **CPU**: Dual-core processor (2.0 GHz+)
- **RAM**: 4GB (2GB available for application)
- **Storage**: 2GB free space
- **Network**: Internet connection for web features (optional)

### Recommended Requirements
- **CPU**: Quad-core processor (2.5 GHz+)
- **RAM**: 8GB (4GB available for application)
- **Storage**: 5GB free space
- **Microphone**: For voice input
- **Speakers/Headphones**: For voice output

## Installation

### 1. Clone or Download
```bash
git clone <repository-url>
cd virtual-assistant
```

### 2. Run Setup
```bash
python setup.py
```

The setup script will:
- Check Python version compatibility
- Install required packages
- Download speech recognition models
- Create necessary directories
- Test the installation

### 3. Manual Installation (if setup fails)
```bash
# Install requirements
pip install -r requirements.txt

# Install spaCy model
python -m spacy download en_core_web_sm

# Create directories
mkdir -p data/logs models
```

## Usage

### Command Line Interface
```bash
python main.py
```

### Graphical User Interface
```bash
python gui.py
```

### Voice Commands
Say "Hey Buddy" followed by your command:

- **Tasks**: "Remind me to call John at 3 PM"
- **Search**: "Search for Python tutorials"
- **Messages**: "Send message to Mom saying I'll be late"
- **Files**: "Open my documents folder"
- **Ideas**: "Give me some productivity ideas"
- **System**: "What's my system status?"

### Text Commands
Type commands directly in the GUI or use the same natural language as voice commands.

## Configuration

### Email Setup
```python
# In the GUI, go to Settings or use the API
email_client.setup_email(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    imap_server="imap.gmail.com", 
    imap_port=993,
    username="<EMAIL>",
    password="your-app-password",
    use_tls=True
)
```

### WhatsApp Setup
WhatsApp integration uses pywhatkit which opens WhatsApp Web. Ensure you're logged into WhatsApp Web in your default browser.

### Performance Tuning
The assistant automatically detects your system capabilities and adjusts settings:

- **Low-end systems**: Reduced memory usage, smaller models
- **Mid-range systems**: Balanced performance and features
- **High-end systems**: Full features with larger models

## Project Structure

```
virtual_assistant/
├── core/                   # Core engine components
│   ├── speech_engine.py    # Speech recognition/synthesis
│   ├── nlp_processor.py    # Natural language processing
│   ├── context_manager.py  # Conversation context
│   └── task_scheduler.py   # Background task scheduling
├── features/               # Feature modules
│   ├── task_manager.py     # Task management
│   ├── web_searcher.py     # Web search functionality
│   ├── whatsapp_bot.py     # WhatsApp integration
│   ├── email_client.py     # Email management
│   ├── file_manager.py     # File operations
│   └── idea_generator.py   # Idea suggestions
├── utils/                  # Utility modules
│   ├── config.py          # Configuration management
│   ├── logger.py          # Logging system
│   └── memory_monitor.py  # Performance monitoring
├── data/                   # Data storage
│   ├── assistant.db       # SQLite database
│   └── logs/              # Log files
├── models/                 # AI models
│   └── vosk-model-*/      # Speech recognition model
├── main.py                # Command-line interface
├── gui.py                 # Graphical interface
├── setup.py               # Installation script
└── requirements.txt       # Python dependencies
```

## Performance Optimization

### Memory Management
- Automatic model caching and cleanup
- Garbage collection optimization
- Memory usage monitoring
- Configurable memory limits

### CPU Efficiency
- Thread pool management
- Async I/O operations
- Model lazy loading
- Background task scheduling

### Battery Life
- Idle state optimization
- Efficient wake word detection
- Minimal background processing
- Power-aware scheduling

## Troubleshooting

### Common Issues

**Speech recognition not working:**
- Check microphone permissions
- Verify Vosk model installation
- Test audio input devices

**High memory usage:**
- Reduce context window size
- Lower model cache timeout
- Use smaller spaCy model

**WhatsApp messages failing:**
- Ensure WhatsApp Web is logged in
- Check phone number format
- Verify pywhatkit installation

**Email not working:**
- Check email configuration
- Verify app passwords for Gmail
- Test SMTP/IMAP settings

### Performance Issues
```python
# Check system status
from utils.memory_monitor import memory_monitor
stats = memory_monitor.get_performance_stats()
print(stats)
```

### Logs
Check log files in `data/logs/` for detailed error information.

## Development

### Adding New Features
1. Create feature module in `features/`
2. Add intent patterns to `nlp_processor.py`
3. Implement handler in `main.py`
4. Update GUI if needed

### Custom Intents
```python
from core.nlp_processor import nlp_processor
nlp_processor.add_custom_intent("my_intent", ["pattern1", "pattern2"])
```

### API Usage
```python
from features.task_manager import task_manager
from features.web_searcher import web_searcher

# Create task
task_id = task_manager.create_task("My task", "Description")

# Search web
results = web_searcher.search("Python programming")
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review log files for errors

## Acknowledgments

- **Vosk** for offline speech recognition
- **spaCy** for natural language processing
- **pywhatkit** for WhatsApp integration
- **APScheduler** for task scheduling
- **Rich** for beautiful console output

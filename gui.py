"""
Simple GUI interface for the Virtual Assistant
Provides a basic graphical interface for interaction
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import queue
from datetime import datetime
from typing import Optional
from utils.config import config
from utils.logger import logger
from core.speech_engine import speech_engine
from core.nlp_processor import nlp_processor
from core.context_manager import context_manager
from features.task_manager import task_manager, TaskStatus
from main import VirtualAssistant

class AssistantGUI:
    """Simple GUI for the Virtual Assistant"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.assistant: Optional[VirtualAssistant] = None
        self.message_queue = queue.Queue()
        
        self._setup_ui()
        self._setup_assistant()
        
        # Start message processing
        self.root.after(100, self._process_messages)
    
    def _setup_ui(self):
        """Setup the user interface"""
        self.root.title("Virtual Assistant - Buddy")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Virtual Assistant - Buddy", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="5")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="Initializing...")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.listening_indicator = ttk.Label(status_frame, text="●", foreground="red")
        self.listening_indicator.grid(row=0, column=1, sticky=tk.E)
        
        # Chat area
        chat_frame = ttk.LabelFrame(main_frame, text="Conversation", padding="5")
        chat_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        chat_frame.columnconfigure(0, weight=1)
        chat_frame.rowconfigure(0, weight=1)
        
        self.chat_area = scrolledtext.ScrolledText(chat_frame, height=15, state=tk.DISABLED)
        self.chat_area.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Input frame
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        self.input_entry = ttk.Entry(input_frame, font=('Arial', 10))
        self.input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        self.input_entry.bind('<Return>', self._on_send_text)
        
        self.send_button = ttk.Button(input_frame, text="Send", command=self._on_send_text)
        self.send_button.grid(row=0, column=1)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        self.voice_button = ttk.Button(button_frame, text="🎤 Voice", command=self._toggle_voice)
        self.voice_button.grid(row=0, column=0, padx=(0, 5))
        
        self.tasks_button = ttk.Button(button_frame, text="📋 Tasks", command=self._show_tasks)
        self.tasks_button.grid(row=0, column=1, padx=(0, 5))
        
        self.settings_button = ttk.Button(button_frame, text="⚙️ Settings", command=self._show_settings)
        self.settings_button.grid(row=0, column=2, padx=(0, 5))
        
        self.quit_button = ttk.Button(button_frame, text="❌ Quit", command=self._on_quit)
        self.quit_button.grid(row=0, column=3)
        
        # Initial message
        self._add_message("Assistant", "Hello! I'm your virtual assistant. How can I help you today?")
    
    def _setup_assistant(self):
        """Setup the assistant in a separate thread"""
        def init_assistant():
            try:
                self.assistant = VirtualAssistant()
                self.message_queue.put(("status", "Ready"))
                self.message_queue.put(("listening", True))
            except Exception as e:
                self.message_queue.put(("error", f"Failed to initialize assistant: {e}"))
        
        threading.Thread(target=init_assistant, daemon=True).start()
    
    def _process_messages(self):
        """Process messages from the assistant thread"""
        try:
            while True:
                msg_type, content = self.message_queue.get_nowait()
                
                if msg_type == "status":
                    self.status_label.config(text=content)
                elif msg_type == "listening":
                    color = "green" if content else "red"
                    self.listening_indicator.config(foreground=color)
                elif msg_type == "response":
                    self._add_message("Assistant", content)
                elif msg_type == "error":
                    self._add_message("System", f"Error: {content}")
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(100, self._process_messages)
    
    def _add_message(self, sender: str, message: str):
        """Add a message to the chat area"""
        self.chat_area.config(state=tk.NORMAL)
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {sender}: {message}\n"
        
        self.chat_area.insert(tk.END, formatted_message)
        self.chat_area.see(tk.END)
        self.chat_area.config(state=tk.DISABLED)
    
    def _on_send_text(self, event=None):
        """Handle text input"""
        text = self.input_entry.get().strip()
        if not text:
            return
        
        self.input_entry.delete(0, tk.END)
        self._add_message("You", text)
        
        # Process with assistant
        if self.assistant:
            def process_text():
                try:
                    intent = nlp_processor.process_text(text)
                    response = self.assistant._handle_intent(intent)
                    
                    # Add to context
                    context_manager.add_conversation_turn(
                        user_input=text,
                        intent=intent.name,
                        confidence=intent.confidence,
                        assistant_response=response,
                        entities=intent.entities
                    )
                    
                    self.message_queue.put(("response", response))
                    
                except Exception as e:
                    self.message_queue.put(("error", str(e)))
            
            threading.Thread(target=process_text, daemon=True).start()
    
    def _toggle_voice(self):
        """Toggle voice recognition"""
        if speech_engine.listening:
            speech_engine.stop_listening()
            self.voice_button.config(text="🎤 Start Voice")
            self.message_queue.put(("listening", False))
        else:
            def voice_callback(text):
                self.message_queue.put(("response", f"Heard: {text}"))
                # Process the voice input
                self._process_voice_input(text)
            
            speech_engine.start_listening(voice_callback)
            self.voice_button.config(text="🎤 Stop Voice")
            self.message_queue.put(("listening", True))
    
    def _process_voice_input(self, text: str):
        """Process voice input"""
        if speech_engine.is_wake_word(text):
            self.message_queue.put(("response", "Wake word detected! Listening for command..."))
        else:
            # Process as regular command
            self._add_message("You (Voice)", text)
            if self.assistant:
                threading.Thread(
                    target=lambda: self.assistant._process_command(text),
                    daemon=True
                ).start()
    
    def _show_tasks(self):
        """Show tasks window"""
        tasks_window = tk.Toplevel(self.root)
        tasks_window.title("Tasks")
        tasks_window.geometry("600x400")
        
        # Create task list
        task_frame = ttk.Frame(tasks_window, padding="10")
        task_frame.pack(fill=tk.BOTH, expand=True)
        
        # Task listbox
        task_listbox = tk.Listbox(task_frame, height=15)
        task_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Load tasks
        tasks = task_manager.get_tasks(limit=20)
        for task in tasks:
            status_icon = "✓" if task.status == TaskStatus.COMPLETED else "○"
            task_listbox.insert(tk.END, f"{status_icon} {task.title}")
        
        # Buttons
        button_frame = ttk.Frame(task_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Refresh", 
                  command=lambda: self._refresh_tasks(task_listbox)).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Close", 
                  command=tasks_window.destroy).pack(side=tk.RIGHT)
    
    def _refresh_tasks(self, listbox):
        """Refresh task list"""
        listbox.delete(0, tk.END)
        tasks = task_manager.get_tasks(limit=20)
        for task in tasks:
            status_icon = "✓" if task.status == TaskStatus.COMPLETED else "○"
            listbox.insert(tk.END, f"{status_icon} {task.title}")
    
    def _show_settings(self):
        """Show settings window"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings")
        settings_window.geometry("400x300")
        
        settings_frame = ttk.Frame(settings_window, padding="10")
        settings_frame.pack(fill=tk.BOTH, expand=True)
        
        # Wake word setting
        ttk.Label(settings_frame, text="Wake Word:").grid(row=0, column=0, sticky=tk.W, pady=5)
        wake_word_var = tk.StringVar(value=config.get("speech.wake_word", "hey buddy"))
        wake_word_entry = ttk.Entry(settings_frame, textvariable=wake_word_var)
        wake_word_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # Voice feedback setting
        voice_feedback_var = tk.BooleanVar(value=config.get("features.voice_feedback", True))
        ttk.Checkbutton(settings_frame, text="Voice Feedback", 
                       variable=voice_feedback_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Auto reminders setting
        auto_reminders_var = tk.BooleanVar(value=config.get("features.auto_reminders", True))
        ttk.Checkbutton(settings_frame, text="Auto Reminders", 
                       variable=auto_reminders_var).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # Save button
        def save_settings():
            config.set("speech.wake_word", wake_word_var.get())
            config.set("features.voice_feedback", voice_feedback_var.get())
            config.set("features.auto_reminders", auto_reminders_var.get())
            messagebox.showinfo("Settings", "Settings saved successfully!")
            settings_window.destroy()
        
        ttk.Button(settings_frame, text="Save", command=save_settings).grid(row=3, column=0, pady=10)
        ttk.Button(settings_frame, text="Cancel", command=settings_window.destroy).grid(row=3, column=1, pady=10)
        
        settings_frame.columnconfigure(1, weight=1)
    
    def _on_quit(self):
        """Handle quit button"""
        if self.assistant:
            self.assistant.stop()
        self.root.quit()
    
    def run(self):
        """Run the GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self._on_quit()

def main():
    """Main entry point for GUI"""
    app = AssistantGUI()
    app.run()

if __name__ == "__main__":
    main()

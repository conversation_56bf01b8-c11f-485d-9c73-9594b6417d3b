"""
Logging configuration for the Virtual Assistant
Provides structured logging with performance monitoring
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional
from rich.logging import <PERSON><PERSON><PERSON>ler
from rich.console import Console

class AssistantLogger:
    """Custom logger for the virtual assistant"""
    
    def __init__(self, name: str = "VirtualAssistant"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.console = Console()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging configuration"""
        # Clear any existing handlers
        self.logger.handlers.clear()
        
        # Set logging level
        self.logger.setLevel(logging.DEBUG)
        
        # Create logs directory
        logs_dir = Path(__file__).parent.parent / "data" / "logs"
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # File handler for detailed logs
        log_file = logs_dir / f"assistant_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler with Rich formatting
        console_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=False,
            rich_tracebacks=True
        )
        console_handler.setLevel(logging.INFO)
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, extra=kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, extra=kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, extra=kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, extra=kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message, extra=kwargs)
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log performance metrics"""
        self.info(f"Performance: {operation} took {duration:.3f}s", **kwargs)
    
    def log_memory_usage(self, memory_mb: float, operation: Optional[str] = None):
        """Log memory usage"""
        if operation:
            self.debug(f"Memory usage after {operation}: {memory_mb:.1f}MB")
        else:
            self.debug(f"Current memory usage: {memory_mb:.1f}MB")

# Global logger instance
logger = AssistantLogger()

"""
Email management for the Virtual Assistant
Handles reading and sending emails
"""

import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import decode_header
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from utils.config import config
from utils.logger import logger
from core.context_manager import context_manager

class EmailMessage:
    """Represents an email message"""
    
    def __init__(self, subject: str, sender: str, recipient: str, 
                 body: str, date: datetime, message_id: str = ""):
        self.subject = subject
        self.sender = sender
        self.recipient = recipient
        self.body = body
        self.date = date
        self.message_id = message_id

class EmailClient:
    """Handles email operations"""
    
    def __init__(self):
        self.smtp_server = None
        self.imap_server = None
        self.email_config = self._load_email_config()
        self.connected = False
    
    def _load_email_config(self) -> Dict[str, Any]:
        """Load email configuration from user preferences"""
        return {
            'smtp_server': context_manager.get_preference('email.smtp_server', ''),
            'smtp_port': context_manager.get_preference('email.smtp_port', 587),
            'imap_server': context_manager.get_preference('email.imap_server', ''),
            'imap_port': context_manager.get_preference('email.imap_port', 993),
            'username': context_manager.get_preference('email.username', ''),
            'password': context_manager.get_preference('email.password', ''),
            'use_tls': context_manager.get_preference('email.use_tls', True)
        }
    
    def setup_email(self, smtp_server: str, smtp_port: int, 
                   imap_server: str, imap_port: int,
                   username: str, password: str, use_tls: bool = True) -> bool:
        """Setup email configuration"""
        try:
            self.email_config = {
                'smtp_server': smtp_server,
                'smtp_port': smtp_port,
                'imap_server': imap_server,
                'imap_port': imap_port,
                'username': username,
                'password': password,
                'use_tls': use_tls
            }
            
            # Save to preferences
            for key, value in self.email_config.items():
                context_manager.set_preference(f'email.{key}', value, 'email')
            
            # Test connection
            if self._test_connection():
                logger.info("Email configuration setup successful")
                return True
            else:
                logger.error("Email configuration test failed")
                return False
                
        except Exception as e:
            logger.error(f"Failed to setup email: {e}")
            return False
    
    def _test_connection(self) -> bool:
        """Test email server connections"""
        try:
            # Test SMTP
            smtp = smtplib.SMTP(self.email_config['smtp_server'], 
                               self.email_config['smtp_port'])
            if self.email_config['use_tls']:
                smtp.starttls()
            smtp.login(self.email_config['username'], 
                      self.email_config['password'])
            smtp.quit()
            
            # Test IMAP
            imap = imaplib.IMAP4_SSL(self.email_config['imap_server'], 
                                    self.email_config['imap_port'])
            imap.login(self.email_config['username'], 
                      self.email_config['password'])
            imap.logout()
            
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False
    
    def send_email(self, to_email: str, subject: str, body: str, 
                  cc: List[str] = None, bcc: List[str] = None) -> bool:
        """Send an email"""
        if not self._is_configured():
            logger.error("Email not configured")
            return False
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['username']
            msg['To'] = to_email
            msg['Subject'] = subject
            
            if cc:
                msg['Cc'] = ', '.join(cc)
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Connect to SMTP server
            smtp = smtplib.SMTP(self.email_config['smtp_server'], 
                               self.email_config['smtp_port'])
            
            if self.email_config['use_tls']:
                smtp.starttls()
            
            smtp.login(self.email_config['username'], 
                      self.email_config['password'])
            
            # Send email
            recipients = [to_email]
            if cc:
                recipients.extend(cc)
            if bcc:
                recipients.extend(bcc)
            
            smtp.sendmail(self.email_config['username'], recipients, msg.as_string())
            smtp.quit()
            
            logger.info(f"Email sent to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def get_recent_emails(self, folder: str = 'INBOX', 
                         days_back: int = 7, limit: int = 20) -> List[EmailMessage]:
        """Get recent emails from specified folder"""
        if not self._is_configured():
            logger.error("Email not configured")
            return []
        
        emails = []
        
        try:
            # Connect to IMAP server
            imap = imaplib.IMAP4_SSL(self.email_config['imap_server'], 
                                    self.email_config['imap_port'])
            imap.login(self.email_config['username'], 
                      self.email_config['password'])
            
            # Select folder
            imap.select(folder)
            
            # Search for recent emails
            cutoff_date = (datetime.now() - timedelta(days=days_back)).strftime('%d-%b-%Y')
            search_criteria = f'(SINCE "{cutoff_date}")'
            
            status, messages = imap.search(None, search_criteria)
            
            if status == 'OK':
                message_ids = messages[0].split()
                
                # Get most recent emails (reverse order)
                for msg_id in reversed(message_ids[-limit:]):
                    try:
                        status, msg_data = imap.fetch(msg_id, '(RFC822)')
                        
                        if status == 'OK':
                            email_msg = email.message_from_bytes(msg_data[0][1])
                            parsed_email = self._parse_email(email_msg)
                            if parsed_email:
                                emails.append(parsed_email)
                    
                    except Exception as e:
                        logger.debug(f"Error parsing email {msg_id}: {e}")
                        continue
            
            imap.logout()
            
        except Exception as e:
            logger.error(f"Failed to get recent emails: {e}")
        
        return emails
    
    def _parse_email(self, email_msg) -> Optional[EmailMessage]:
        """Parse email message object"""
        try:
            # Get subject
            subject = self._decode_header(email_msg.get('Subject', ''))
            
            # Get sender
            sender = self._decode_header(email_msg.get('From', ''))
            
            # Get recipient
            recipient = self._decode_header(email_msg.get('To', ''))
            
            # Get date
            date_str = email_msg.get('Date', '')
            date = email.utils.parsedate_to_datetime(date_str) if date_str else datetime.now()
            
            # Get message ID
            message_id = email_msg.get('Message-ID', '')
            
            # Get body
            body = self._extract_body(email_msg)
            
            return EmailMessage(
                subject=subject,
                sender=sender,
                recipient=recipient,
                body=body,
                date=date,
                message_id=message_id
            )
            
        except Exception as e:
            logger.error(f"Error parsing email: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """Decode email header"""
        try:
            decoded_parts = decode_header(header)
            decoded_header = ''
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_header += part.decode(encoding)
                    else:
                        decoded_header += part.decode('utf-8', errors='ignore')
                else:
                    decoded_header += part
            
            return decoded_header
            
        except Exception as e:
            logger.debug(f"Error decoding header: {e}")
            return header
    
    def _extract_body(self, email_msg) -> str:
        """Extract email body text"""
        body = ""
        
        try:
            if email_msg.is_multipart():
                for part in email_msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition'))
                    
                    if content_type == 'text/plain' and 'attachment' not in content_disposition:
                        charset = part.get_content_charset() or 'utf-8'
                        body = part.get_payload(decode=True).decode(charset, errors='ignore')
                        break
            else:
                charset = email_msg.get_content_charset() or 'utf-8'
                body = email_msg.get_payload(decode=True).decode(charset, errors='ignore')
            
            # Clean up body
            body = body.strip()
            if len(body) > 1000:
                body = body[:1000] + "..."
            
        except Exception as e:
            logger.debug(f"Error extracting email body: {e}")
            body = "Could not extract email body"
        
        return body
    
    def search_emails(self, query: str, folder: str = 'INBOX', 
                     limit: int = 10) -> List[EmailMessage]:
        """Search emails by subject or sender"""
        if not self._is_configured():
            logger.error("Email not configured")
            return []
        
        emails = []
        
        try:
            imap = imaplib.IMAP4_SSL(self.email_config['imap_server'], 
                                    self.email_config['imap_port'])
            imap.login(self.email_config['username'], 
                      self.email_config['password'])
            
            imap.select(folder)
            
            # Search by subject and sender
            search_criteria = f'(OR (SUBJECT "{query}") (FROM "{query}"))'
            status, messages = imap.search(None, search_criteria)
            
            if status == 'OK':
                message_ids = messages[0].split()
                
                for msg_id in reversed(message_ids[-limit:]):
                    try:
                        status, msg_data = imap.fetch(msg_id, '(RFC822)')
                        
                        if status == 'OK':
                            email_msg = email.message_from_bytes(msg_data[0][1])
                            parsed_email = self._parse_email(email_msg)
                            if parsed_email:
                                emails.append(parsed_email)
                    
                    except Exception as e:
                        logger.debug(f"Error parsing search result {msg_id}: {e}")
                        continue
            
            imap.logout()
            
        except Exception as e:
            logger.error(f"Failed to search emails: {e}")
        
        return emails
    
    def get_unread_count(self, folder: str = 'INBOX') -> int:
        """Get count of unread emails"""
        if not self._is_configured():
            return 0
        
        try:
            imap = imaplib.IMAP4_SSL(self.email_config['imap_server'], 
                                    self.email_config['imap_port'])
            imap.login(self.email_config['username'], 
                      self.email_config['password'])
            
            imap.select(folder)
            
            status, messages = imap.search(None, 'UNSEEN')
            
            if status == 'OK':
                count = len(messages[0].split()) if messages[0] else 0
            else:
                count = 0
            
            imap.logout()
            return count
            
        except Exception as e:
            logger.error(f"Failed to get unread count: {e}")
            return 0
    
    def _is_configured(self) -> bool:
        """Check if email is properly configured"""
        required_fields = ['smtp_server', 'imap_server', 'username', 'password']
        return all(self.email_config.get(field) for field in required_fields)
    
    def get_status(self) -> Dict[str, Any]:
        """Get email client status"""
        return {
            'configured': self._is_configured(),
            'username': self.email_config.get('username', ''),
            'smtp_server': self.email_config.get('smtp_server', ''),
            'imap_server': self.email_config.get('imap_server', ''),
            'connection_test': self._test_connection() if self._is_configured() else False
        }

# Global email client instance
email_client = EmailClient()

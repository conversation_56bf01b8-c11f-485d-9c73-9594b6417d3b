"""
Test script for the Virtual Assistant
Verifies core functionality without requiring full setup
"""

import sys
import time
from pathlib import Path

def test_imports():
    """Test if all modules can be imported"""
    print("Testing module imports...")
    
    try:
        from utils.config import config
        print("✓ Config module")
        
        from utils.logger import logger
        print("✓ Logger module")
        
        from utils.memory_monitor import memory_monitor
        print("✓ Memory monitor module")
        
        from core.nlp_processor import nlp_processor
        print("✓ NLP processor module")
        
        from core.context_manager import context_manager
        print("✓ Context manager module")
        
        from core.task_scheduler import task_scheduler
        print("✓ Task scheduler module")
        
        from features.task_manager import task_manager
        print("✓ Task manager module")
        
        from features.web_searcher import web_searcher
        print("✓ Web searcher module")
        
        from features.whatsapp_bot import whatsapp_bot
        print("✓ WhatsApp bot module")
        
        from features.email_client import email_client
        print("✓ Email client module")
        
        from features.file_manager import file_manager
        print("✓ File manager module")
        
        from features.idea_generator import idea_generator
        print("✓ Idea generator module")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_configuration():
    """Test configuration system"""
    print("\nTesting configuration...")
    
    try:
        from utils.config import config
        
        # Test system detection
        system_info = config.system_info
        print(f"✓ System tier: {system_info['performance_tier']}")
        print(f"✓ Memory: {system_info['memory_gb']:.1f}GB")
        print(f"✓ CPUs: {system_info['cpu_count']}")
        
        # Test configuration access
        wake_word = config.get("speech.wake_word", "hey buddy")
        print(f"✓ Wake word: {wake_word}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_nlp():
    """Test NLP processing"""
    print("\nTesting NLP processor...")
    
    try:
        from core.nlp_processor import nlp_processor
        
        # Test intent detection
        test_phrases = [
            "remind me to call John",
            "search for Python tutorials", 
            "what are my tasks",
            "hello there",
            "goodbye"
        ]
        
        for phrase in test_phrases:
            intent = nlp_processor.process_text(phrase)
            print(f"✓ '{phrase}' -> {intent.name} ({intent.confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"✗ NLP error: {e}")
        return False

def test_task_manager():
    """Test task management"""
    print("\nTesting task manager...")
    
    try:
        from features.task_manager import task_manager, TaskPriority
        
        # Create a test task
        task_id = task_manager.create_task(
            title="Test Task",
            description="This is a test task",
            priority=TaskPriority.MEDIUM
        )
        
        if task_id:
            print(f"✓ Created task: {task_id}")
            
            # Get the task
            task = task_manager.get_task(task_id)
            if task:
                print(f"✓ Retrieved task: {task.title}")
            
            # Get task statistics
            stats = task_manager.get_task_statistics()
            print(f"✓ Task statistics: {stats.get('total_tasks', 0)} total tasks")
            
            return True
        else:
            print("✗ Failed to create task")
            return False
        
    except Exception as e:
        print(f"✗ Task manager error: {e}")
        return False

def test_web_search():
    """Test web search (requires internet)"""
    print("\nTesting web search...")
    
    try:
        from features.web_searcher import web_searcher
        
        # Test quick answer
        answer = web_searcher.quick_answer("what is Python programming")
        if answer:
            print(f"✓ Quick answer: {answer[:100]}...")
        else:
            print("○ No quick answer (normal for some queries)")
        
        # Test search
        results = web_searcher.search("Python programming", num_results=2)
        if results:
            print(f"✓ Search results: {len(results)} found")
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result.title[:50]}...")
        else:
            print("○ No search results (may be network issue)")
        
        return True
        
    except Exception as e:
        print(f"✗ Web search error: {e}")
        return False

def test_idea_generator():
    """Test idea generation"""
    print("\nTesting idea generator...")
    
    try:
        from features.idea_generator import idea_generator
        
        # Test idea generation
        ideas = idea_generator.generate_ideas(category="productivity", num_ideas=2)
        if ideas:
            print(f"✓ Generated {len(ideas)} ideas:")
            for i, idea in enumerate(ideas, 1):
                print(f"  {i}. {idea[:60]}...")
        
        # Test daily suggestion
        daily = idea_generator.get_daily_suggestion()
        print(f"✓ Daily suggestion: {daily[:60]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Idea generator error: {e}")
        return False

def test_memory_monitor():
    """Test memory monitoring"""
    print("\nTesting memory monitor...")
    
    try:
        from utils.memory_monitor import memory_monitor
        
        # Get performance stats
        stats = memory_monitor.get_performance_stats()
        print(f"✓ Memory usage: {stats['memory_mb']:.1f}MB")
        print(f"✓ CPU usage: {stats['cpu_percent']:.1f}%")
        print(f"✓ System memory: {stats['system_info']['total_memory_gb']:.1f}GB total")
        
        return True
        
    except Exception as e:
        print(f"✗ Memory monitor error: {e}")
        return False

def test_file_operations():
    """Test file operations"""
    print("\nTesting file manager...")
    
    try:
        from features.file_manager import file_manager
        
        # Test listing current directory
        files = file_manager.list_files(".", limit=5)
        if files:
            print(f"✓ Listed {len(files)} files in current directory")
        
        # Test disk usage
        usage = file_manager.get_disk_usage()
        if usage:
            print(f"✓ Disk usage: {usage['usage_percent']:.1f}% used")
        
        return True
        
    except Exception as e:
        print(f"✗ File manager error: {e}")
        return False

def main():
    """Run all tests"""
    print("Virtual Assistant Test Suite")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("NLP Processing", test_nlp),
        ("Task Manager", test_task_manager),
        ("Web Search", test_web_search),
        ("Idea Generator", test_idea_generator),
        ("Memory Monitor", test_memory_monitor),
        ("File Operations", test_file_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The assistant is ready to use.")
        print("\nTo start the assistant:")
        print("  Command line: python main.py")
        print("  GUI interface: python gui.py")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("You may need to run setup.py or install missing dependencies.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
File management system for the Virtual Assistant
Handles file operations and system interactions
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Optional
import mimetypes
from datetime import datetime
from utils.config import config
from utils.logger import logger

class FileManager:
    """Handles file system operations"""
    
    def __init__(self):
        self.allowed_extensions = {
            'text': ['.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
            'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'],
            'audio': ['.mp3', '.wav', '.flac', '.aac'],
            'video': ['.mp4', '.avi', '.mkv', '.mov']
        }
        
        # Safe directories for operations
        self.safe_directories = [
            Path.home() / "Documents",
            Path.home() / "Desktop",
            Path.home() / "Downloads",
            Path.home() / "Pictures",
            Path.home() / "Music",
            Path.home() / "Videos"
        ]
    
    def list_files(self, directory: str, pattern: str = "*", 
                  include_hidden: bool = False) -> List[Dict[str, Any]]:
        """List files in a directory"""
        try:
            dir_path = Path(directory).expanduser().resolve()
            
            if not self._is_safe_path(dir_path):
                logger.warning(f"Access denied to directory: {directory}")
                return []
            
            if not dir_path.exists():
                logger.error(f"Directory does not exist: {directory}")
                return []
            
            files = []
            
            for item in dir_path.iterdir():
                # Skip hidden files unless requested
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                # Apply pattern filter
                if pattern != "*" and pattern not in item.name:
                    continue
                
                try:
                    stat = item.stat()
                    file_info = {
                        'name': item.name,
                        'path': str(item),
                        'type': 'directory' if item.is_dir() else 'file',
                        'size': stat.st_size if item.is_file() else 0,
                        'modified': datetime.fromtimestamp(stat.st_mtime),
                        'extension': item.suffix.lower() if item.is_file() else '',
                        'mime_type': mimetypes.guess_type(str(item))[0] if item.is_file() else None
                    }
                    files.append(file_info)
                    
                except (OSError, PermissionError) as e:
                    logger.debug(f"Cannot access {item}: {e}")
                    continue
            
            # Sort by type (directories first) then by name
            files.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            
            return files
            
        except Exception as e:
            logger.error(f"Failed to list files in {directory}: {e}")
            return []
    
    def find_files(self, search_term: str, search_directory: str = None, 
                  file_type: str = None) -> List[Dict[str, Any]]:
        """Search for files by name"""
        if search_directory is None:
            search_directory = str(Path.home())
        
        try:
            search_path = Path(search_directory).expanduser().resolve()
            
            if not self._is_safe_path(search_path):
                logger.warning(f"Access denied to search directory: {search_directory}")
                return []
            
            found_files = []
            search_term = search_term.lower()
            
            # Get file extensions for type filter
            extensions = []
            if file_type and file_type in self.allowed_extensions:
                extensions = self.allowed_extensions[file_type]
            
            for root, dirs, files in os.walk(search_path):
                # Limit search depth to avoid performance issues
                if len(Path(root).parts) - len(search_path.parts) > 3:
                    continue
                
                for file in files:
                    if search_term in file.lower():
                        file_path = Path(root) / file
                        
                        # Apply type filter
                        if extensions and file_path.suffix.lower() not in extensions:
                            continue
                        
                        try:
                            stat = file_path.stat()
                            file_info = {
                                'name': file,
                                'path': str(file_path),
                                'type': 'file',
                                'size': stat.st_size,
                                'modified': datetime.fromtimestamp(stat.st_mtime),
                                'extension': file_path.suffix.lower(),
                                'directory': str(Path(root))
                            }
                            found_files.append(file_info)
                            
                        except (OSError, PermissionError):
                            continue
                
                # Limit results to avoid overwhelming output
                if len(found_files) >= 50:
                    break
            
            return found_files
            
        except Exception as e:
            logger.error(f"Failed to search for files: {e}")
            return []
    
    def open_file(self, file_path: str) -> bool:
        """Open a file with the default application"""
        try:
            path = Path(file_path).expanduser().resolve()
            
            if not path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            if not self._is_safe_path(path):
                logger.warning(f"Access denied to file: {file_path}")
                return False
            
            # Open with default application
            if os.name == 'nt':  # Windows
                os.startfile(str(path))
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', str(path)])
            
            logger.info(f"Opened file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to open file {file_path}: {e}")
            return False
    
    def create_file(self, file_path: str, content: str = "") -> bool:
        """Create a new file with optional content"""
        try:
            path = Path(file_path).expanduser().resolve()
            
            if not self._is_safe_path(path.parent):
                logger.warning(f"Access denied to directory: {path.parent}")
                return False
            
            # Create parent directories if they don't exist
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content to file
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Created file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create file {file_path}: {e}")
            return False
    
    def delete_file(self, file_path: str) -> bool:
        """Delete a file"""
        try:
            path = Path(file_path).expanduser().resolve()
            
            if not path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            if not self._is_safe_path(path):
                logger.warning(f"Access denied to file: {file_path}")
                return False
            
            if path.is_file():
                path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            else:
                logger.error(f"Path is not a file: {file_path}")
                return False
            
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def copy_file(self, source_path: str, destination_path: str) -> bool:
        """Copy a file to a new location"""
        try:
            src = Path(source_path).expanduser().resolve()
            dst = Path(destination_path).expanduser().resolve()
            
            if not src.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            if not self._is_safe_path(src) or not self._is_safe_path(dst.parent):
                logger.warning("Access denied to source or destination")
                return False
            
            # Create destination directory if it doesn't exist
            dst.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src, dst)
            logger.info(f"Copied file from {source_path} to {destination_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to copy file: {e}")
            return False
    
    def move_file(self, source_path: str, destination_path: str) -> bool:
        """Move a file to a new location"""
        try:
            src = Path(source_path).expanduser().resolve()
            dst = Path(destination_path).expanduser().resolve()
            
            if not src.exists():
                logger.error(f"Source file does not exist: {source_path}")
                return False
            
            if not self._is_safe_path(src) or not self._is_safe_path(dst.parent):
                logger.warning("Access denied to source or destination")
                return False
            
            # Create destination directory if it doesn't exist
            dst.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src), str(dst))
            logger.info(f"Moved file from {source_path} to {destination_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to move file: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a file"""
        try:
            path = Path(file_path).expanduser().resolve()
            
            if not path.exists():
                logger.error(f"File does not exist: {file_path}")
                return None
            
            if not self._is_safe_path(path):
                logger.warning(f"Access denied to file: {file_path}")
                return None
            
            stat = path.stat()
            
            return {
                'name': path.name,
                'path': str(path),
                'type': 'directory' if path.is_dir() else 'file',
                'size': stat.st_size,
                'size_human': self._format_size(stat.st_size),
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'accessed': datetime.fromtimestamp(stat.st_atime),
                'extension': path.suffix.lower() if path.is_file() else '',
                'mime_type': mimetypes.guess_type(str(path))[0] if path.is_file() else None,
                'permissions': oct(stat.st_mode)[-3:],
                'parent': str(path.parent)
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return None
    
    def create_directory(self, directory_path: str) -> bool:
        """Create a new directory"""
        try:
            path = Path(directory_path).expanduser().resolve()
            
            if not self._is_safe_path(path.parent):
                logger.warning(f"Access denied to parent directory: {path.parent}")
                return False
            
            path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create directory {directory_path}: {e}")
            return False
    
    def _is_safe_path(self, path: Path) -> bool:
        """Check if a path is safe for operations"""
        try:
            path = path.resolve()
            
            # Check if path is within safe directories
            for safe_dir in self.safe_directories:
                try:
                    path.relative_to(safe_dir.resolve())
                    return True
                except ValueError:
                    continue
            
            # Allow access to current working directory
            try:
                path.relative_to(Path.cwd())
                return True
            except ValueError:
                pass
            
            return False
            
        except Exception:
            return False
    
    def _format_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def get_disk_usage(self, path: str = None) -> Dict[str, Any]:
        """Get disk usage information"""
        if path is None:
            path = str(Path.home())
        
        try:
            usage = shutil.disk_usage(path)
            
            return {
                'total': usage.total,
                'used': usage.total - usage.free,
                'free': usage.free,
                'total_human': self._format_size(usage.total),
                'used_human': self._format_size(usage.total - usage.free),
                'free_human': self._format_size(usage.free),
                'usage_percent': ((usage.total - usage.free) / usage.total) * 100
            }
            
        except Exception as e:
            logger.error(f"Failed to get disk usage: {e}")
            return {}

# Global file manager instance
file_manager = FileManager()

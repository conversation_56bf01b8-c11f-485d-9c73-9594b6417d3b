"""
Task scheduling system for the Virtual Assistant
Handles background tasks, reminders, and periodic operations
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Callable, Dict, Any, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from utils.config import config
from utils.logger import logger

class TaskScheduler:
    """Manages scheduled tasks and reminders"""
    
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.running = False
        self.scheduled_tasks = {}
        self.reminder_callback = None
        
        # Configure scheduler
        self.scheduler.configure(
            timezone='UTC',
            job_defaults={
                'coalesce': True,
                'max_instances': 3,
                'misfire_grace_time': 30
            }
        )
    
    def start(self):
        """Start the task scheduler"""
        if not self.running:
            try:
                self.scheduler.start()
                self.running = True
                
                # Schedule periodic cleanup
                self._schedule_periodic_tasks()
                
                logger.info("Task scheduler started")
            except Exception as e:
                logger.error(f"Failed to start task scheduler: {e}")
    
    def stop(self):
        """Stop the task scheduler"""
        if self.running:
            try:
                self.scheduler.shutdown(wait=False)
                self.running = False
                logger.info("Task scheduler stopped")
            except Exception as e:
                logger.error(f"Failed to stop task scheduler: {e}")
    
    def _schedule_periodic_tasks(self):
        """Schedule recurring system tasks"""
        # Memory cleanup every 10 minutes
        self.scheduler.add_job(
            func=self._memory_cleanup_task,
            trigger=IntervalTrigger(minutes=10),
            id='memory_cleanup',
            name='Memory Cleanup',
            replace_existing=True
        )
        
        # Context cleanup daily at 2 AM
        self.scheduler.add_job(
            func=self._context_cleanup_task,
            trigger=CronTrigger(hour=2, minute=0),
            id='context_cleanup',
            name='Context Cleanup',
            replace_existing=True
        )
        
        # Performance monitoring every 5 minutes
        self.scheduler.add_job(
            func=self._performance_monitoring_task,
            trigger=IntervalTrigger(minutes=5),
            id='performance_monitoring',
            name='Performance Monitoring',
            replace_existing=True
        )
    
    def schedule_reminder(self, task_id: str, message: str, 
                         scheduled_time: datetime, callback: Callable = None) -> bool:
        """Schedule a one-time reminder"""
        try:
            job_id = f"reminder_{task_id}"
            
            self.scheduler.add_job(
                func=self._reminder_handler,
                trigger=DateTrigger(run_date=scheduled_time),
                args=[task_id, message, callback],
                id=job_id,
                name=f"Reminder: {message[:50]}...",
                replace_existing=True
            )
            
            self.scheduled_tasks[task_id] = {
                'job_id': job_id,
                'message': message,
                'scheduled_time': scheduled_time,
                'type': 'reminder'
            }
            
            logger.info(f"Scheduled reminder for {scheduled_time}: {message}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule reminder: {e}")
            return False
    
    def schedule_recurring_task(self, task_id: str, func: Callable, 
                              interval_minutes: int, **kwargs) -> bool:
        """Schedule a recurring task"""
        try:
            job_id = f"recurring_{task_id}"
            
            self.scheduler.add_job(
                func=func,
                trigger=IntervalTrigger(minutes=interval_minutes),
                id=job_id,
                name=f"Recurring: {task_id}",
                replace_existing=True,
                **kwargs
            )
            
            self.scheduled_tasks[task_id] = {
                'job_id': job_id,
                'function': func.__name__,
                'interval_minutes': interval_minutes,
                'type': 'recurring'
            }
            
            logger.info(f"Scheduled recurring task: {task_id} every {interval_minutes} minutes")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule recurring task: {e}")
            return False
    
    def schedule_cron_task(self, task_id: str, func: Callable, 
                          cron_expression: str, **kwargs) -> bool:
        """Schedule a task using cron expression"""
        try:
            job_id = f"cron_{task_id}"
            
            # Parse cron expression
            parts = cron_expression.split()
            if len(parts) != 5:
                raise ValueError("Invalid cron expression format")
            
            minute, hour, day, month, day_of_week = parts
            
            self.scheduler.add_job(
                func=func,
                trigger=CronTrigger(
                    minute=minute,
                    hour=hour,
                    day=day,
                    month=month,
                    day_of_week=day_of_week
                ),
                id=job_id,
                name=f"Cron: {task_id}",
                replace_existing=True,
                **kwargs
            )
            
            self.scheduled_tasks[task_id] = {
                'job_id': job_id,
                'function': func.__name__,
                'cron_expression': cron_expression,
                'type': 'cron'
            }
            
            logger.info(f"Scheduled cron task: {task_id} with expression {cron_expression}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule cron task: {e}")
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a scheduled task"""
        if task_id not in self.scheduled_tasks:
            logger.warning(f"Task not found: {task_id}")
            return False
        
        try:
            job_id = self.scheduled_tasks[task_id]['job_id']
            self.scheduler.remove_job(job_id)
            del self.scheduled_tasks[task_id]
            
            logger.info(f"Cancelled task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel task: {e}")
            return False
    
    def get_scheduled_tasks(self) -> Dict[str, Any]:
        """Get list of all scheduled tasks"""
        return self.scheduled_tasks.copy()
    
    def get_next_reminders(self, hours_ahead: int = 24) -> list:
        """Get upcoming reminders within specified hours"""
        upcoming = []
        cutoff_time = datetime.now() + timedelta(hours=hours_ahead)
        
        for task_id, task_info in self.scheduled_tasks.items():
            if task_info['type'] == 'reminder':
                scheduled_time = task_info['scheduled_time']
                if scheduled_time <= cutoff_time:
                    upcoming.append({
                        'task_id': task_id,
                        'message': task_info['message'],
                        'scheduled_time': scheduled_time,
                        'time_until': scheduled_time - datetime.now()
                    })
        
        # Sort by scheduled time
        upcoming.sort(key=lambda x: x['scheduled_time'])
        return upcoming
    
    def set_reminder_callback(self, callback: Callable):
        """Set callback function for reminder notifications"""
        self.reminder_callback = callback
    
    def _reminder_handler(self, task_id: str, message: str, callback: Callable = None):
        """Handle reminder execution"""
        logger.info(f"Reminder triggered: {message}")
        
        # Remove from scheduled tasks
        if task_id in self.scheduled_tasks:
            del self.scheduled_tasks[task_id]
        
        # Call specific callback if provided
        if callback:
            try:
                callback(task_id, message)
            except Exception as e:
                logger.error(f"Error in reminder callback: {e}")
        
        # Call global reminder callback
        if self.reminder_callback:
            try:
                self.reminder_callback(task_id, message)
            except Exception as e:
                logger.error(f"Error in global reminder callback: {e}")
    
    def _memory_cleanup_task(self):
        """Periodic memory cleanup task"""
        try:
            from utils.memory_monitor import memory_monitor
            memory_monitor.cleanup()
        except Exception as e:
            logger.error(f"Error in memory cleanup task: {e}")
    
    def _context_cleanup_task(self):
        """Periodic context cleanup task"""
        try:
            from core.context_manager import context_manager
            context_manager.cleanup_old_data(days_to_keep=30)
        except Exception as e:
            logger.error(f"Error in context cleanup task: {e}")
    
    def _performance_monitoring_task(self):
        """Periodic performance monitoring task"""
        try:
            from utils.memory_monitor import memory_monitor
            stats = memory_monitor.get_performance_stats()
            
            # Log performance warnings
            if stats['memory_mb'] > config.get("performance.max_memory_mb", 1500):
                logger.warning(f"High memory usage: {stats['memory_mb']:.1f}MB")
            
            if stats['cpu_percent'] > config.get("performance.max_cpu_percent", 50):
                logger.warning(f"High CPU usage: {stats['cpu_percent']:.1f}%")
                
        except Exception as e:
            logger.error(f"Error in performance monitoring task: {e}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get scheduler status information"""
        jobs = self.scheduler.get_jobs()
        
        return {
            'running': self.running,
            'job_count': len(jobs),
            'scheduled_tasks': len(self.scheduled_tasks),
            'next_run_time': min([job.next_run_time for job in jobs]) if jobs else None,
            'jobs': [
                {
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time,
                    'trigger': str(job.trigger)
                }
                for job in jobs
            ]
        }

# Global task scheduler instance
task_scheduler = TaskScheduler()

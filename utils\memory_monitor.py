"""
Memory and performance monitoring for the Virtual Assistant
Tracks resource usage and optimizes performance
"""

import gc
import time
import psutil
import threading
from typing import Dict, Any, Callable
from utils.config import config
from utils.logger import logger

class MemoryMonitor:
    """Monitor and manage memory usage for optimal performance"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        self.last_cleanup = time.time()
        self.model_cache = {}
        self.model_last_used = {}
        
    def start_monitoring(self):
        """Start background memory monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("Memory monitoring started")
    
    def stop_monitoring(self):
        """Stop background memory monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        logger.info("Memory monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                self._check_memory_usage()
                self._cleanup_if_needed()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _check_memory_usage(self):
        """Check current memory usage and take action if needed"""
        memory_info = self.get_memory_info()
        max_memory_mb = config.get("performance.max_memory_mb", 1500)
        
        if memory_info["memory_mb"] > max_memory_mb:
            logger.warning(f"Memory usage ({memory_info['memory_mb']:.1f}MB) exceeds limit ({max_memory_mb}MB)")
            self._emergency_cleanup()
            
            # Notify callbacks
            for callback in self.callbacks:
                try:
                    callback("high_memory", memory_info)
                except Exception as e:
                    logger.error(f"Error in memory callback: {e}")
    
    def _cleanup_if_needed(self):
        """Perform regular cleanup if interval has passed"""
        cleanup_interval = config.get("performance.cleanup_interval", 600)
        
        if time.time() - self.last_cleanup > cleanup_interval:
            self.cleanup()
    
    def cleanup(self):
        """Perform memory cleanup operations"""
        logger.info("Performing memory cleanup")
        
        # Clean up old models from cache
        self._cleanup_model_cache()
        
        # Force garbage collection
        collected = gc.collect()
        logger.debug(f"Garbage collection freed {collected} objects")
        
        self.last_cleanup = time.time()
        
        # Log memory usage after cleanup
        memory_info = self.get_memory_info()
        logger.info(f"Memory usage after cleanup: {memory_info['memory_mb']:.1f}MB")
    
    def _emergency_cleanup(self):
        """Aggressive cleanup when memory usage is too high"""
        logger.warning("Performing emergency memory cleanup")
        
        # Clear all cached models
        self.model_cache.clear()
        self.model_last_used.clear()
        
        # Multiple garbage collection passes
        for _ in range(3):
            gc.collect()
        
        # Notify callbacks about emergency cleanup
        for callback in self.callbacks:
            try:
                callback("emergency_cleanup", {})
            except Exception as e:
                logger.error(f"Error in emergency cleanup callback: {e}")
    
    def _cleanup_model_cache(self):
        """Remove old models from cache"""
        cache_timeout = config.get("performance.model_cache_timeout", 300)
        current_time = time.time()
        
        models_to_remove = []
        for model_name, last_used in self.model_last_used.items():
            if current_time - last_used > cache_timeout:
                models_to_remove.append(model_name)
        
        for model_name in models_to_remove:
            if model_name in self.model_cache:
                del self.model_cache[model_name]
                del self.model_last_used[model_name]
                logger.debug(f"Removed cached model: {model_name}")
    
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory usage information"""
        try:
            memory_info = self.process.memory_info()
            cpu_percent = self.process.cpu_percent()
            
            return {
                "memory_mb": memory_info.rss / 1024 / 1024,
                "memory_percent": self.process.memory_percent(),
                "cpu_percent": cpu_percent,
                "num_threads": self.process.num_threads(),
                "system_memory_percent": psutil.virtual_memory().percent
            }
        except Exception as e:
            logger.error(f"Error getting memory info: {e}")
            return {"memory_mb": 0, "memory_percent": 0, "cpu_percent": 0}
    
    def cache_model(self, name: str, model: Any):
        """Cache a model with automatic cleanup"""
        self.model_cache[name] = model
        self.model_last_used[name] = time.time()
        logger.debug(f"Cached model: {name}")
    
    def get_cached_model(self, name: str):
        """Get a cached model and update last used time"""
        if name in self.model_cache:
            self.model_last_used[name] = time.time()
            return self.model_cache[name]
        return None
    
    def add_callback(self, callback: Callable):
        """Add a callback for memory events"""
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """Remove a memory event callback"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics"""
        memory_info = self.get_memory_info()
        
        return {
            **memory_info,
            "cached_models": len(self.model_cache),
            "monitoring_active": self.monitoring,
            "last_cleanup": self.last_cleanup,
            "system_info": {
                "total_memory_gb": psutil.virtual_memory().total / 1024**3,
                "available_memory_gb": psutil.virtual_memory().available / 1024**3,
                "cpu_count": psutil.cpu_count()
            }
        }

# Global memory monitor instance
memory_monitor = MemoryMonitor()

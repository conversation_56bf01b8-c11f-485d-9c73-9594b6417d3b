"""
WhatsApp integration for the Virtual Assistant
Handles sending messages and basic automation
"""

import time
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from utils.config import config
from utils.logger import logger

try:
    import pywhatkit as pwk
    PYWHATKIT_AVAILABLE = True
except ImportError:
    PYWHATKIT_AVAILABLE = False
    logger.warning("pywhatkit not available - WhatsApp features disabled")

class WhatsAppBot:
    """Handles WhatsApp message automation"""
    
    def __init__(self):
        self.enabled = PYWHATKIT_AVAILABLE
        self.message_history = []
        self.contacts = {}
        self.default_delay = 15  # seconds to wait before sending
        
        if self.enabled:
            logger.info("WhatsApp bot initialized")
        else:
            logger.warning("WhatsApp bot disabled - pywhatkit not available")
    
    def send_message(self, phone_number: str, message: str, 
                    delay_minutes: int = 1) -> bool:
        """Send a WhatsApp message"""
        if not self.enabled:
            logger.error("WhatsApp not available")
            return False
        
        try:
            # Clean phone number
            phone_number = self._clean_phone_number(phone_number)
            
            # Calculate send time
            now = datetime.now()
            send_time = now + timedelta(minutes=delay_minutes)
            
            # Send message using pywhatkit
            pwk.sendwhatmsg(
                phone_no=phone_number,
                message=message,
                time_hour=send_time.hour,
                time_min=send_time.minute,
                wait_time=self.default_delay,
                tab_close=True,
                close_time=3
            )
            
            # Log message
            self.message_history.append({
                'phone_number': phone_number,
                'message': message,
                'sent_at': send_time,
                'status': 'sent'
            })
            
            logger.info(f"WhatsApp message scheduled to {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WhatsApp message: {e}")
            return False
    
    def send_message_now(self, phone_number: str, message: str) -> bool:
        """Send a WhatsApp message immediately"""
        if not self.enabled:
            logger.error("WhatsApp not available")
            return False
        
        try:
            # Clean phone number
            phone_number = self._clean_phone_number(phone_number)
            
            # Send immediately
            pwk.sendwhatmsg_instantly(
                phone_no=phone_number,
                message=message,
                wait_time=self.default_delay,
                tab_close=True,
                close_time=3
            )
            
            # Log message
            self.message_history.append({
                'phone_number': phone_number,
                'message': message,
                'sent_at': datetime.now(),
                'status': 'sent'
            })
            
            logger.info(f"WhatsApp message sent immediately to {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send immediate WhatsApp message: {e}")
            return False
    
    def send_to_group(self, group_id: str, message: str, 
                     delay_minutes: int = 1) -> bool:
        """Send message to a WhatsApp group"""
        if not self.enabled:
            logger.error("WhatsApp not available")
            return False
        
        try:
            # Calculate send time
            now = datetime.now()
            send_time = now + timedelta(minutes=delay_minutes)
            
            # Send to group
            pwk.sendwhatmsg_to_group(
                group_id=group_id,
                message=message,
                time_hour=send_time.hour,
                time_min=send_time.minute,
                wait_time=self.default_delay,
                tab_close=True,
                close_time=3
            )
            
            # Log message
            self.message_history.append({
                'group_id': group_id,
                'message': message,
                'sent_at': send_time,
                'status': 'sent'
            })
            
            logger.info(f"WhatsApp group message scheduled to {group_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WhatsApp group message: {e}")
            return False
    
    def _clean_phone_number(self, phone_number: str) -> str:
        """Clean and format phone number"""
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)
        
        # Ensure it starts with +
        if not cleaned.startswith('+'):
            # Assume it's a US number if no country code
            if len(cleaned) == 10:
                cleaned = '+1' + cleaned
            else:
                cleaned = '+' + cleaned
        
        return cleaned
    
    def add_contact(self, name: str, phone_number: str, 
                   notes: str = "") -> bool:
        """Add a contact to the local contact list"""
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            self.contacts[name.lower()] = {
                'name': name,
                'phone_number': phone_number,
                'notes': notes,
                'added_at': datetime.now()
            }
            
            logger.info(f"Added contact: {name} ({phone_number})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add contact: {e}")
            return False
    
    def get_contact(self, name: str) -> Optional[Dict[str, Any]]:
        """Get contact information by name"""
        return self.contacts.get(name.lower())
    
    def list_contacts(self) -> List[Dict[str, Any]]:
        """Get list of all contacts"""
        return list(self.contacts.values())
    
    def send_to_contact(self, contact_name: str, message: str, 
                       delay_minutes: int = 1) -> bool:
        """Send message to a saved contact"""
        contact = self.get_contact(contact_name)
        if not contact:
            logger.error(f"Contact not found: {contact_name}")
            return False
        
        return self.send_message(
            phone_number=contact['phone_number'],
            message=message,
            delay_minutes=delay_minutes
        )
    
    def schedule_message(self, phone_number: str, message: str, 
                        send_time: datetime) -> bool:
        """Schedule a message for a specific time"""
        if not self.enabled:
            logger.error("WhatsApp not available")
            return False
        
        if send_time <= datetime.now():
            logger.error("Send time must be in the future")
            return False
        
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            pwk.sendwhatmsg(
                phone_no=phone_number,
                message=message,
                time_hour=send_time.hour,
                time_min=send_time.minute,
                wait_time=self.default_delay,
                tab_close=True,
                close_time=3
            )
            
            # Log scheduled message
            self.message_history.append({
                'phone_number': phone_number,
                'message': message,
                'sent_at': send_time,
                'status': 'scheduled'
            })
            
            logger.info(f"WhatsApp message scheduled for {send_time}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to schedule WhatsApp message: {e}")
            return False
    
    def get_message_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent message history"""
        return self.message_history[-limit:]
    
    def clear_message_history(self):
        """Clear message history"""
        self.message_history.clear()
        logger.info("WhatsApp message history cleared")
    
    def send_image(self, phone_number: str, image_path: str, 
                  caption: str = "", delay_minutes: int = 1) -> bool:
        """Send an image via WhatsApp"""
        if not self.enabled:
            logger.error("WhatsApp not available")
            return False
        
        try:
            phone_number = self._clean_phone_number(phone_number)
            
            # Calculate send time
            now = datetime.now()
            send_time = now + timedelta(minutes=delay_minutes)
            
            # Send image
            pwk.sendwhats_image(
                receiver=phone_number,
                img_path=image_path,
                caption=caption,
                wait_time=self.default_delay,
                tab_close=True,
                close_time=3
            )
            
            # Log message
            self.message_history.append({
                'phone_number': phone_number,
                'message': f"Image: {caption}",
                'image_path': image_path,
                'sent_at': send_time,
                'status': 'sent'
            })
            
            logger.info(f"WhatsApp image sent to {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send WhatsApp image: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get WhatsApp bot status"""
        return {
            'enabled': self.enabled,
            'messages_sent': len(self.message_history),
            'contacts_count': len(self.contacts),
            'last_message': self.message_history[-1] if self.message_history else None
        }
    
    def parse_message_command(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse natural language message commands"""
        text = text.lower().strip()
        
        # Pattern: "send message to [contact/number] saying [message]"
        pattern1 = r"send message to (.+?) saying (.+)"
        match = re.search(pattern1, text)
        if match:
            recipient = match.group(1).strip()
            message = match.group(2).strip()
            return {
                'action': 'send_message',
                'recipient': recipient,
                'message': message
            }
        
        # Pattern: "text [contact/number] [message]"
        pattern2 = r"text (.+?) (.+)"
        match = re.search(pattern2, text)
        if match:
            recipient = match.group(1).strip()
            message = match.group(2).strip()
            return {
                'action': 'send_message',
                'recipient': recipient,
                'message': message
            }
        
        # Pattern: "whatsapp [contact/number] [message]"
        pattern3 = r"whatsapp (.+?) (.+)"
        match = re.search(pattern3, text)
        if match:
            recipient = match.group(1).strip()
            message = match.group(2).strip()
            return {
                'action': 'send_message',
                'recipient': recipient,
                'message': message
            }
        
        return None
    
    def execute_command(self, command: Dict[str, Any]) -> bool:
        """Execute a parsed message command"""
        if command['action'] == 'send_message':
            recipient = command['recipient']
            message = command['message']
            
            # Check if recipient is a contact name
            contact = self.get_contact(recipient)
            if contact:
                return self.send_to_contact(recipient, message)
            else:
                # Assume it's a phone number
                return self.send_message(recipient, message)
        
        return False

# Global WhatsApp bot instance
whatsapp_bot = WhatsAppBot()

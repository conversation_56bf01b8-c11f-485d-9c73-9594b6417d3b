"""
Context management for the Virtual Assistant
Handles conversation state, user preferences, and session management
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from utils.config import config
from utils.logger import logger

@dataclass
class ConversationTurn:
    """Represents a single turn in conversation"""
    timestamp: datetime
    user_input: str
    intent: str
    confidence: float
    assistant_response: str
    entities: Dict[str, Any]
    session_id: str

@dataclass
class UserPreference:
    """Represents a user preference"""
    key: str
    value: Any
    category: str
    timestamp: datetime

class ContextManager:
    """Manages conversation context and user preferences"""
    
    def __init__(self):
        self.db_path = Path(config.get("paths.database"))
        self.current_session_id = None
        self.session_start_time = None
        self.conversation_buffer = []
        self.user_preferences = {}
        
        self._init_database()
        self._load_preferences()
        self.start_new_session()
    
    def _init_database(self):
        """Initialize SQLite database for context storage"""
        try:
            # Ensure data directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Conversation history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS conversation_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        user_input TEXT NOT NULL,
                        intent TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        assistant_response TEXT NOT NULL,
                        entities TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        category TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Session metadata table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sessions (
                        session_id TEXT PRIMARY KEY,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        turn_count INTEGER DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_session_id ON conversation_history(session_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON conversation_history(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_intent ON conversation_history(intent)')
                
                conn.commit()
                logger.info("Context database initialized")
                
        except Exception as e:
            logger.error(f"Failed to initialize context database: {e}")
    
    def start_new_session(self) -> str:
        """Start a new conversation session"""
        self.current_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.session_start_time = datetime.now()
        self.conversation_buffer.clear()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sessions (session_id, start_time)
                    VALUES (?, ?)
                ''', (self.current_session_id, self.session_start_time.isoformat()))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to create new session: {e}")
        
        logger.info(f"Started new session: {self.current_session_id}")
        return self.current_session_id
    
    def end_current_session(self):
        """End the current conversation session"""
        if not self.current_session_id:
            return
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE sessions 
                    SET end_time = ?, turn_count = ?
                    WHERE session_id = ?
                ''', (
                    datetime.now().isoformat(),
                    len(self.conversation_buffer),
                    self.current_session_id
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to end session: {e}")
        
        logger.info(f"Ended session: {self.current_session_id}")
        self.current_session_id = None
        self.session_start_time = None
    
    def add_conversation_turn(self, user_input: str, intent: str, confidence: float,
                            assistant_response: str, entities: Dict[str, Any] = None):
        """Add a conversation turn to context"""
        if not self.current_session_id:
            self.start_new_session()
        
        turn = ConversationTurn(
            timestamp=datetime.now(),
            user_input=user_input,
            intent=intent,
            confidence=confidence,
            assistant_response=assistant_response,
            entities=entities or {},
            session_id=self.current_session_id
        )
        
        # Add to buffer
        self.conversation_buffer.append(turn)
        
        # Keep buffer size manageable
        max_buffer_size = config.get("nlp.context_window", 5) * 2
        if len(self.conversation_buffer) > max_buffer_size:
            self.conversation_buffer.pop(0)
        
        # Save to database
        self._save_conversation_turn(turn)
        
        logger.debug(f"Added conversation turn: {intent} ({confidence:.2f})")
    
    def _save_conversation_turn(self, turn: ConversationTurn):
        """Save conversation turn to database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO conversation_history 
                    (session_id, timestamp, user_input, intent, confidence, 
                     assistant_response, entities)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    turn.session_id,
                    turn.timestamp.isoformat(),
                    turn.user_input,
                    turn.intent,
                    turn.confidence,
                    turn.assistant_response,
                    json.dumps(turn.entities)
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to save conversation turn: {e}")
    
    def get_recent_context(self, num_turns: int = 3) -> List[ConversationTurn]:
        """Get recent conversation turns for context"""
        return self.conversation_buffer[-num_turns:] if self.conversation_buffer else []
    
    def get_conversation_history(self, session_id: str = None, 
                               days_back: int = 7) -> List[ConversationTurn]:
        """Get conversation history from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if session_id:
                    cursor.execute('''
                        SELECT session_id, timestamp, user_input, intent, confidence,
                               assistant_response, entities
                        FROM conversation_history
                        WHERE session_id = ?
                        ORDER BY timestamp
                    ''', (session_id,))
                else:
                    cutoff_date = (datetime.now() - timedelta(days=days_back)).isoformat()
                    cursor.execute('''
                        SELECT session_id, timestamp, user_input, intent, confidence,
                               assistant_response, entities
                        FROM conversation_history
                        WHERE timestamp >= ?
                        ORDER BY timestamp DESC
                        LIMIT 100
                    ''', (cutoff_date,))
                
                rows = cursor.fetchall()
                
                history = []
                for row in rows:
                    entities = json.loads(row[6]) if row[6] else {}
                    history.append(ConversationTurn(
                        session_id=row[0],
                        timestamp=datetime.fromisoformat(row[1]),
                        user_input=row[2],
                        intent=row[3],
                        confidence=row[4],
                        assistant_response=row[5],
                        entities=entities
                    ))
                
                return history
                
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []
    
    def set_preference(self, key: str, value: Any, category: str = "general"):
        """Set a user preference"""
        self.user_preferences[key] = UserPreference(
            key=key,
            value=value,
            category=category,
            timestamp=datetime.now()
        )
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO user_preferences 
                    (key, value, category, timestamp, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    key,
                    json.dumps(value),
                    category,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to save preference: {e}")
        
        logger.debug(f"Set preference: {key} = {value}")
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """Get a user preference"""
        if key in self.user_preferences:
            return self.user_preferences[key].value
        return default
    
    def _load_preferences(self):
        """Load user preferences from database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT key, value, category, timestamp
                    FROM user_preferences
                    ORDER BY updated_at DESC
                ''')
                
                for row in cursor.fetchall():
                    key, value_json, category, timestamp = row
                    try:
                        value = json.loads(value_json)
                        self.user_preferences[key] = UserPreference(
                            key=key,
                            value=value,
                            category=category,
                            timestamp=datetime.fromisoformat(timestamp)
                        )
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse preference value for {key}")
                
                logger.info(f"Loaded {len(self.user_preferences)} user preferences")
                
        except Exception as e:
            logger.error(f"Failed to load preferences: {e}")
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of current context"""
        recent_intents = [turn.intent for turn in self.conversation_buffer[-5:]]
        
        return {
            "session_id": self.current_session_id,
            "session_duration": (datetime.now() - self.session_start_time).total_seconds() 
                               if self.session_start_time else 0,
            "turn_count": len(self.conversation_buffer),
            "recent_intents": recent_intents,
            "preferences_count": len(self.user_preferences),
            "last_intent": recent_intents[-1] if recent_intents else None
        }
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Clean up old conversation data"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete old conversation history
                cursor.execute('''
                    DELETE FROM conversation_history
                    WHERE timestamp < ?
                ''', (cutoff_date,))
                
                # Delete old sessions
                cursor.execute('''
                    DELETE FROM sessions
                    WHERE start_time < ?
                ''', (cutoff_date,))
                
                deleted_rows = cursor.rowcount
                conn.commit()
                
                logger.info(f"Cleaned up {deleted_rows} old conversation records")
                
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")

# Global context manager instance
context_manager = ContextManager()
